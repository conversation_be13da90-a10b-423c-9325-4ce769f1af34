# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/net/marvell,88x5113-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell 88x5113 PHY Device Tree Bindings

maintainers:
  - Linux Kernel Network Developers <<EMAIL>>

description: |
  The Marvell 88x5113 is a high-speed Ethernet PHY that supports retimer mode
  operation for 25G and 10G Ethernet applications. It communicates via I2C
  instead of traditional MDIO and can serve multiple MAC controllers through
  lane sharing.

  This PHY is designed to replace Inphi PHY drivers in DPAA2 systems and
  provides enhanced retimer functionality with automatic lane recovery.

allOf:
  - $ref: ethernet-phy.yaml#

properties:
  compatible:
    enum:
      - marvell,88x5113
      - marvell,88x5113-i2c

  reg:
    description: |
      For MDIO registration: PHY address on MDIO bus (used for driver binding)
      For I2C device: I2C slave address (actual communication address)
    maxItems: 1

  marvell,i2c-bus:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: I2C bus number for PHY communication
    minimum: 0
    maximum: 7

  marvell,i2c-address:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: I2C slave address for PHY communication
    minimum: 0x70
    maximum: 0x7F
    default: 0x77

  marvell,retimer-mode:
    type: boolean
    description: Enable retimer mode operation

  marvell,lane-count:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Number of lanes supported by this PHY instance
    minimum: 1
    maximum: 4
    default: 4

  marvell,max-speed:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Maximum speed in Mbps
    enum: [10000, 25000]
    default: 25000

  marvell,vco-codes:
    $ref: /schemas/types.yaml#/definitions/uint16-array
    description: VCO codes for each lane (4 values)
    minItems: 4
    maxItems: 4

  marvell,poll-interval:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Polling interval for lane monitoring in milliseconds
    minimum: 1000
    maximum: 10000
    default: 2500

  marvell,lane-assignment:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: |
      Lane assignment for this PHY instance. Specifies which physical
      lanes (0-3) are used by this MAC controller.
    minItems: 1
    maxItems: 4

  marvell,device-id:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Device ID for identification
    default: 0x5113

  marvell,max-lanes:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Maximum number of lanes supported by the physical device
    minimum: 1
    maximum: 4
    default: 4

  interrupt-parent:
    description: Interrupt controller for PHY interrupts

  interrupts:
    description: Interrupt line for PHY status changes
    maxItems: 1

  interrupt-names:
    description: Name for the interrupt
    items:
      - const: phy-interrupt

required:
  - compatible
  - reg

if:
  properties:
    compatible:
      contains:
        const: marvell,88x5113-i2c
then:
  required:
    - marvell,device-id
    - marvell,retimer-mode
else:
  required:
    - marvell,i2c-bus
    - marvell,i2c-address
    - marvell,retimer-mode

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/gpio/gpio.h>

    // Example 1: MDIO registration with I2C communication
    mdio {
        #address-cells = <1>;
        #size-cells = <0>;

        phy_88x5113_1: ethernet-phy@1 {
            compatible = "marvell,88x5113";
            reg = <0x1>;
            
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
            marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
            marvell,poll-interval = <2500>;
            marvell,lane-assignment = <0 1>;
        };

        phy_88x5113_2: ethernet-phy@2 {
            compatible = "marvell,88x5113";
            reg = <0x2>;
            
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
            marvell,lane-assignment = <2 3>;
        };
    };

  - |
    // Example 2: I2C device configuration
    i2c1 {
        #address-cells = <1>;
        #size-cells = <0>;

        phy_88x5113_i2c: phy@77 {
            compatible = "marvell,88x5113-i2c";
            reg = <0x77>;
            
            marvell,device-id = <0x5113>;
            marvell,retimer-mode;
            marvell,max-lanes = <4>;
            marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
            
            interrupt-parent = <&gpio2>;
            interrupts = <10 IRQ_TYPE_LEVEL_LOW>;
            interrupt-names = "phy-interrupt";
        };
    };

  - |
    // Example 3: DPAA2 MAC integration
    fsl-mc {
        dpmacs {
            #address-cells = <1>;
            #size-cells = <0>;

            dpmac@5 {
                compatible = "fsl,qoriq-mc-dpmac";
                reg = <5>;
                phy-handle = <&phy_88x5113_1>;
                phy-connection-type = "25gbase-kr";
                managed = "in-band-status";
            };

            dpmac@6 {
                compatible = "fsl,qoriq-mc-dpmac";
                reg = <6>;
                phy-handle = <&phy_88x5113_2>;
                phy-connection-type = "25gbase-kr";
                managed = "in-band-status";
            };
        };
    };
