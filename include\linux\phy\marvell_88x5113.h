/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Marvell 88x5113 PHY driver integration header
 *
 * This header provides integration functions for MAC drivers
 * to interface with the Marvell 88x5113 PHY driver.
 */

#ifndef _MARVELL_88X5113_H_
#define _MARVELL_88X5113_H_

#include <linux/phy.h>
#include <linux/phylink.h>

/* PHY ID for 88x5113 */
#define PHY_ID_88X5113         0x02B09AA0
#define PHY_ID_88X5113_MASK    0xFFFFFFF0

/* 88x5113 specific capabilities */
#define MXD_88X5113_RETIMER_MODE    BIT(0)
#define MXD_88X5113_LANE_RECOVERY   BIT(1)
#define MXD_88X5113_I2C_COMM        BIT(2)

/* Lane definitions */
#define MXD_88X5113_MAX_LANES       4
#define MXD_88X5113_ALL_LANES       4

/* Speed definitions for retimer mode */
#define MXD_88X5113_SPEED_25G       25000
#define MXD_88X5113_SPEED_10G       10000

/* Integration status codes */
enum mxd_88x5113_status {
    MXD_88X5113_OK = 0,
    MXD_88X5113_FAIL = -1,
    MXD_88X5113_NOT_READY = -2,
    MXD_88X5113_LANE_ERROR = -3,
};

/* 88x5113 PHY information structure */
struct mxd_88x5113_info {
    u16 device_id;
    u16 revision;
    u8 lane_count;
    bool retimer_mode;
    u32 max_speed;
    u16 vco_codes[MXD_88X5113_MAX_LANES];
    u16 lane_status[MXD_88X5113_MAX_LANES];
};

/* 88x5113 PHY operations for MAC driver integration */
struct mxd_88x5113_ops {
    /* Initialize PHY for retimer mode */
    int (*init_retimer)(struct phy_device *phydev);
    
    /* Get PHY information */
    int (*get_info)(struct phy_device *phydev, struct mxd_88x5113_info *info);
    
    /* Check lane status */
    int (*check_lanes)(struct phy_device *phydev, u16 *lane_status);
    
    /* Trigger lane recovery */
    int (*recover_lanes)(struct phy_device *phydev, int lane_mask);
    
    /* Get link status for retimer mode */
    int (*get_link_status)(struct phy_device *phydev, bool *link_up);
    
    /* Set speed for retimer mode */
    int (*set_speed)(struct phy_device *phydev, u32 speed);
};

#ifdef CONFIG_MARVELL_88X5113_PHY

/* Function prototypes for 88x5113 PHY integration */

/**
 * mxd_88x5113_is_supported - Check if PHY is 88x5113
 * @phydev: PHY device
 *
 * Returns true if the PHY is a Marvell 88x5113, false otherwise
 */
bool mxd_88x5113_is_supported(struct phy_device *phydev);

/**
 * mxd_88x5113_get_ops - Get 88x5113 PHY operations
 * @phydev: PHY device
 *
 * Returns pointer to 88x5113 operations structure or NULL if not supported
 */
const struct mxd_88x5113_ops *mxd_88x5113_get_ops(struct phy_device *phydev);

/**
 * mxd_88x5113_init_for_mac - Initialize 88x5113 for MAC integration
 * @phydev: PHY device
 * @mac_name: Name of the MAC driver for identification
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_init_for_mac(struct phy_device *phydev, const char *mac_name);

/**
 * mxd_88x5113_configure_retimer - Configure PHY for retimer mode
 * @phydev: PHY device
 * @speed: Target speed (10000 or 25000 Mbps)
 * @lane_count: Number of lanes to configure
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_configure_retimer(struct phy_device *phydev, u32 speed, u8 lane_count);

/**
 * mxd_88x5113_get_lane_status - Get status of all lanes
 * @phydev: PHY device
 * @lane_status: Array to store lane status (must be MXD_88X5113_MAX_LANES size)
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_get_lane_status(struct phy_device *phydev, u16 *lane_status);

/**
 * mxd_88x5113_recover_lanes - Trigger lane recovery
 * @phydev: PHY device
 * @lane_mask: Bitmask of lanes to recover (bit 0-3 for lanes 0-3, 0xF for all)
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_recover_lanes(struct phy_device *phydev, int lane_mask);

/**
 * mxd_88x5113_is_link_up - Check if retimer link is established
 * @phydev: PHY device
 *
 * Returns true if link is up, false otherwise
 */
bool mxd_88x5113_is_link_up(struct phy_device *phydev);

/**
 * mxd_88x5113_get_speed - Get current operating speed
 * @phydev: PHY device
 *
 * Returns speed in Mbps (10000 or 25000), or 0 if link is down
 */
u32 mxd_88x5113_get_speed(struct phy_device *phydev);

/**
 * mxd_88x5113_register_mac_callback - Register MAC driver callback
 * @phydev: PHY device
 * @callback: Callback function for link state changes
 * @data: Private data for callback
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_register_mac_callback(struct phy_device *phydev,
                                      void (*callback)(void *data, bool link_up),
                                      void *data);

/**
 * mxd_88x5113_unregister_mac_callback - Unregister MAC driver callback
 * @phydev: PHY device
 */
void mxd_88x5113_unregister_mac_callback(struct phy_device *phydev);

#else /* !CONFIG_MARVELL_88X5113_PHY */

/* Stub functions when 88x5113 PHY driver is not enabled */

static inline bool mxd_88x5113_is_supported(struct phy_device *phydev)
{
    return false;
}

static inline const struct mxd_88x5113_ops *mxd_88x5113_get_ops(struct phy_device *phydev)
{
    return NULL;
}

static inline int mxd_88x5113_init_for_mac(struct phy_device *phydev, const char *mac_name)
{
    return -ENODEV;
}

static inline int mxd_88x5113_configure_retimer(struct phy_device *phydev, u32 speed, u8 lane_count)
{
    return -ENODEV;
}

static inline int mxd_88x5113_get_lane_status(struct phy_device *phydev, u16 *lane_status)
{
    return -ENODEV;
}

static inline int mxd_88x5113_recover_lanes(struct phy_device *phydev, int lane_mask)
{
    return -ENODEV;
}

static inline bool mxd_88x5113_is_link_up(struct phy_device *phydev)
{
    return false;
}

static inline u32 mxd_88x5113_get_speed(struct phy_device *phydev)
{
    return 0;
}

static inline int mxd_88x5113_register_mac_callback(struct phy_device *phydev,
                                                    void (*callback)(void *data, bool link_up),
                                                    void *data)
{
    return -ENODEV;
}

static inline void mxd_88x5113_unregister_mac_callback(struct phy_device *phydev)
{
}

#endif /* CONFIG_MARVELL_88X5113_PHY */

/* Helper macros for MAC drivers */
#define MXD_88X5113_LANE_MASK_ALL    0xF
#define MXD_88X5113_LANE_MASK(lane)  BIT(lane)

/* Check if a specific lane is locked */
#define MXD_88X5113_LANE_IS_LOCKED(status) ((status) & BIT(15))

/* Check if a specific lane is ready */
#define MXD_88X5113_LANE_IS_READY(status)  ((status) & BIT(14))

#endif /* _MARVELL_88X5113_H_ */
