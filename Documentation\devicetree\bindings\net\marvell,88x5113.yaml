# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/net/marvell,88x5113.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell 88x5113 PHY

maintainers:
  - Linux Kernel Network Developers <<EMAIL>>

description: |
  The Marvell 88x5113 is a high-speed PHY that supports retimer mode operation.
  It communicates via I2C instead of traditional MDIO protocol when MDIO/MDC
  pins are connected to CPLD.

allOf:
  - $ref: ethernet-phy.yaml#

properties:
  compatible:
    enum:
      - marvell,88x5113

  reg:
    maxItems: 1

  marvell,i2c-bus:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      I2C bus number for communication with the PHY.
      Default is 1 if not specified.
    default: 1

  marvell,i2c-address:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      I2C address for the PHY device.
      Default is 0x77 if not specified.
    default: 0x77

  marvell,retimer-mode:
    type: boolean
    description: |
      Enable retimer mode operation. When set, the PHY operates
      as a retimer instead of a traditional PHY.

  marvell,lane-count:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Number of lanes to configure for retimer operation.
      Valid values are 1-4.
    minimum: 1
    maximum: 4
    default: 4

  marvell,vco-codes:
    $ref: /schemas/types.yaml#/definitions/uint16-array
    description: |
      VCO codes for each lane. Array of up to 4 values.
      Default VCO code is 0x0186 for 25G retimer mode.
    maxItems: 4

  marvell,poll-interval:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Polling interval in milliseconds for lane status monitoring.
      Default is 2500ms if not specified.
    default: 2500

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    mdio {
        #address-cells = <1>;
        #size-cells = <0>;

        phy@0 {
            compatible = "marvell,88x5113";
            reg = <0>;
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
            marvell,poll-interval = <2500>;
        };
    };

  - |
    mdio {
        #address-cells = <1>;
        #size-cells = <0>;

        phy@1 {
            compatible = "marvell,88x5113";
            reg = <1>;
            marvell,i2c-bus = <2>;
            marvell,retimer-mode;
            marvell,lane-count = <2>;
        };
    };
