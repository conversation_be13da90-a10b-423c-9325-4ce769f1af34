.. SPDX-License-Identifier: GPL-2.0

=================================
Marvell 88x5113 PHY Driver
=================================

Overview
========

The Marvell 88x5113 PHY driver supports the 88x5113 high-speed PHY in retimer mode.
This PHY is designed for high-speed Ethernet applications and supports 10G and 25G
operation across up to 4 lanes.

Key Features
============

* Retimer mode operation (non-PHY mode)
* I2C communication instead of traditional MDIO
* Support for 10G and 25G speeds
* Up to 4 lanes configuration
* Automatic lane recovery and monitoring
* Device tree configuration support

Hardware Configuration
======================

The 88x5113 PHY can be configured in different modes. This driver specifically
supports retimer mode where:

* MDIO and MDC pins are connected to CPLD
* Communication occurs via I2C protocol
* Default I2C address is 0x77
* Supports 3 MAC connections

I2C Communication
=================

Unlike traditional PHY drivers that use MDIO protocol, the 88x5113 driver
communicates via I2C when the MDIO/MDC pins are connected to CPLD.

The I2C communication protocol:
* Uses standard I2C transactions
* Register access via MMD (MDIO Manageable Device) addressing
* Automatic retry mechanism for reliability
* Default I2C bus is 1 (configurable via device tree)

Device Tree Configuration
=========================

Basic configuration::

    mdio {
        #address-cells = <1>;
        #size-cells = <0>;

        phy@0 {
            compatible = "marvell,88x5113";
            reg = <0>;
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <4>;
        };
    };

Advanced configuration with custom VCO codes::

    phy@1 {
        compatible = "marvell,88x5113";
        reg = <1>;
        marvell,i2c-bus = <2>;
        marvell,retimer-mode;
        marvell,lane-count = <4>;
        marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
        marvell,poll-interval = <2500>;
    };

Device Tree Properties
======================

Required properties:
* ``compatible``: Must be "marvell,88x5113"
* ``reg``: PHY address on the MDIO bus

Optional properties:
* ``marvell,i2c-bus``: I2C bus number (default: 1)
* ``marvell,i2c-address``: I2C device address (default: 0x77)
* ``marvell,retimer-mode``: Enable retimer mode operation
* ``marvell,lane-count``: Number of lanes (1-4, default: 4)
* ``marvell,vco-codes``: Array of VCO codes for each lane
* ``marvell,poll-interval``: Lane monitoring interval in ms (default: 2500)

Driver Architecture
===================

The driver consists of several key components:

1. **I2C Communication Layer**
   - Replaces traditional MDIO read/write operations
   - Implements retry mechanism for reliability
   - Handles MMD register addressing

2. **MXD API Integration**
   - Simplified MXD device structure
   - Function pointers for read/write operations
   - Device initialization and configuration

3. **Lane Management**
   - Individual lane configuration and monitoring
   - Automatic lane recovery on link loss
   - VCO code management per lane

4. **PHY Operations**
   - Standard Linux PHY framework integration
   - Link status reporting
   - Speed and duplex configuration

5. **Workqueue Monitoring**
   - Periodic lane status checking
   - Automatic recovery on failures
   - Configurable polling interval

Retimer Mode Operation
======================

In retimer mode, the 88x5113 operates as a signal repeater rather than a
traditional PHY. Key characteristics:

* No autonegotiation
* Fixed speed configuration (10G or 25G)
* Signal regeneration and retiming
* Multiple lane support
* Transparent to higher layers

Lane Recovery
=============

The driver implements automatic lane recovery similar to the inphi.c reference:

1. **Monitoring**: Continuous monitoring of lane lock status
2. **Detection**: Automatic detection of lane failures
3. **Recovery**: Individual or bulk lane recovery procedures
4. **Reconfiguration**: Restoration of retimer mode settings

Supported Speeds
================

* 25000 Mbps (25G) - Primary mode
* 10000 Mbps (10G) - Secondary mode

The driver defaults to 25G retimer mode but can be configured for 10G operation.

Testing
=======

A test program is provided at ``tools/testing/selftests/net/marvell_88x5113_test.c``
to verify driver functionality:

* Driver detection and information
* Link status monitoring
* Sysfs attribute verification

Usage::

    # Compile the test
    gcc -o marvell_88x5113_test tools/testing/selftests/net/marvell_88x5113_test.c

    # Run the test (default interface: eth0)
    ./marvell_88x5113_test

    # Test specific interface
    ./marvell_88x5113_test eth1

Troubleshooting
===============

Common issues and solutions:

1. **I2C Communication Failures**
   - Verify I2C bus number and address
   - Check I2C bus availability
   - Ensure proper device tree configuration

2. **Lane Lock Issues**
   - Check VCO codes configuration
   - Verify signal integrity
   - Monitor lane recovery logs

3. **Driver Not Loading**
   - Ensure CONFIG_MARVELL_88X5113_PHY=y/m
   - Check I2C subsystem availability
   - Verify device tree binding

Debug Information
=================

Enable debug output::

    echo 8 > /proc/sys/kernel/printk
    dmesg | grep 88x5113

Monitor lane status::

    cat /sys/class/net/eth0/carrier
    cat /sys/class/net/eth0/operstate

References
==========

* Marvell 88x5113 datasheet
* MXD API documentation
* Linux PHY framework documentation
* I2C subsystem documentation
