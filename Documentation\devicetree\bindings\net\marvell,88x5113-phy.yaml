# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/net/marvell,88x5113-phy.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell 88x5113 PHY Device Tree Bindings

maintainers:
  - Linux Kernel Network Developers <<EMAIL>>

description: |
  The Marvell 88x5113 is a high-speed PHY device that supports retimer mode
  operation for 25G and 10G Ethernet interfaces. This PHY uses I2C communication
  instead of traditional MDIO for register access and configuration.

  Key features:
  - Retimer mode operation for 25G/10G speeds
  - I2C communication interface (address 0x77)
  - 4-lane SerDes support
  - 25GBASE-CR interface support
  - Integration with DPAA2 MAC drivers

properties:
  compatible:
    enum:
      - marvell,88x5113
      - marvell,88x5113-i2c

  reg:
    maxItems: 1
    description: |
      For MDIO bus: MDIO address of the PHY (used for device tree organization)
      For I2C bus: I2C slave address (typically 0x77)

  marvell,i2c-bus:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: I2C bus number for actual PHY communication
    default: 1

  marvell,i2c-address:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: I2C slave address for PHY communication
    default: 0x77

  marvell,retimer-mode:
    type: boolean
    description: Enable retimer mode operation

  marvell,lane-count:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Number of SerDes lanes to configure
    minimum: 1
    maximum: 4
    default: 4

  marvell,max-speed:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Maximum speed in Mbps
    enum: [10000, 25000]
    default: 25000

  marvell,interface-type:
    $ref: /schemas/types.yaml#/definitions/string
    description: Interface type for the PHY
    enum:
      - "25gbase-cr"
      - "25gbase-kr"
      - "10gbase-cr"
      - "10gbase-kr"
    default: "25gbase-cr"

  marvell,vco-codes:
    $ref: /schemas/types.yaml#/definitions/uint16-array
    description: VCO codes for each lane (4 values)
    minItems: 4
    maxItems: 4

  marvell,lane-assignment:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: Lane assignment for this PHY instance
    minItems: 1
    maxItems: 4

  marvell,poll-interval:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Polling interval in milliseconds for link monitoring
    default: 2500

  marvell,dpmac-id:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Associated DPMAC ID for DPAA2 integration
    minimum: 1
    maximum: 24

  marvell,dpmac-name:
    $ref: /schemas/types.yaml#/definitions/string
    description: Associated DPMAC name for identification

  marvell,device-id:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Device ID for I2C device identification
    default: 0x5113

  marvell,max-lanes:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Maximum number of lanes supported by the device
    default: 4

  interrupt-parent:
    description: Interrupt controller for PHY interrupts

  interrupts:
    maxItems: 1
    description: Interrupt line for PHY status changes

  interrupt-names:
    items:
      - const: phy-interrupt

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    
    /* MDIO bus configuration */
    mdio {
        #address-cells = <1>;
        #size-cells = <0>;
        
        phy_88x5113_1: ethernet-phy@1 {
            compatible = "marvell,88x5113";
            reg = <0x1>;
            
            /* I2C configuration for actual communication */
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            
            /* Retimer mode configuration */
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
            marvell,interface-type = "25gbase-cr";
            marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
            
            /* Lane assignment for DPMAC3 */
            marvell,lane-assignment = <0 1>;
            marvell,dpmac-id = <3>;
            marvell,dpmac-name = "dpmac3";
        };
    };

  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    
    /* I2C bus configuration */
    i2c1 {
        #address-cells = <1>;
        #size-cells = <0>;
        
        phy_88x5113_i2c: phy@77 {
            compatible = "marvell,88x5113-i2c";
            reg = <0x77>;
            marvell,device-id = <0x5113>;
            marvell,retimer-mode;
            marvell,max-lanes = <4>;
            marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
            interrupt-parent = <&gpio2>;
            interrupts = <10 IRQ_TYPE_LEVEL_LOW>;
            interrupt-names = "phy-interrupt";
        };
    };

  - |
    /* Complete LX2160A integration example */
    &dpmac3 {
        phy-handle = <&phy_88x5113_1>;
        phy-connection-type = "25gbase-cr";
        managed = "in-band-status";
    };
    
    &emdio2 {
        status = "okay";
        
        phy_88x5113_1: ethernet-phy@1 {
            compatible = "marvell,88x5113";
            reg = <0x1>;
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
            marvell,interface-type = "25gbase-cr";
            marvell,lane-assignment = <0 1>;
            marvell,dpmac-id = <3>;
        };
    };
