// SPDX-License-Identifier: GPL-2.0
/*
 * Test program for DPAA2 Ethernet and Marvell 88x5113 PHY integration
 *
 * This program tests the integration between DPAA2 Ethernet driver
 * and the 88x5113 PHY driver, verifying that the PHY replacement
 * from Inphi to 88x5113 works correctly.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <linux/ethtool.h>
#include <linux/sockios.h>
#include <net/if.h>
#include <errno.h>
#include <dirent.h>

#define MAX_INTERFACES 10
#define DPAA2_INTERFACE_PREFIX "eth"

struct dpaa2_test_result {
    char interface[IFNAMSIZ];
    int phy_detected;
    int retimer_mode;
    int link_status;
    int speed_25g;
    char driver_name[64];
    char phy_info[128];
};

/* Check if interface is DPAA2 */
static int is_dpaa2_interface(const char *interface)
{
    char path[256];
    char driver_name[64];
    FILE *file;
    int ret = 0;

    snprintf(path, sizeof(path), "/sys/class/net/%s/device/driver", interface);
    
    if (readlink(path, driver_name, sizeof(driver_name) - 1) > 0) {
        if (strstr(driver_name, "dpaa2") || strstr(driver_name, "fsl-mc")) {
            ret = 1;
        }
    }

    return ret;
}

/* Get driver information */
static int get_driver_info(const char *interface, struct dpaa2_test_result *result)
{
    int sock;
    struct ifreq ifr;
    struct ethtool_drvinfo drvinfo;
    int ret = 0;

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        return -1;
    }

    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);

    memset(&drvinfo, 0, sizeof(drvinfo));
    drvinfo.cmd = ETHTOOL_GDRVINFO;
    ifr.ifr_data = (char *)&drvinfo;

    if (ioctl(sock, SIOCETHTOOL, &ifr) == 0) {
        strncpy(result->driver_name, drvinfo.driver, sizeof(result->driver_name) - 1);
        snprintf(result->phy_info, sizeof(result->phy_info), 
                 "Driver: %s, Version: %s, Bus: %s", 
                 drvinfo.driver, drvinfo.version, drvinfo.bus_info);
        ret = 0;
    } else {
        ret = -1;
    }

    close(sock);
    return ret;
}

/* Check PHY and speed information */
static int check_phy_speed(const char *interface, struct dpaa2_test_result *result)
{
    int sock;
    struct ifreq ifr;
    struct ethtool_cmd cmd;
    int ret = 0;

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        return -1;
    }

    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);

    memset(&cmd, 0, sizeof(cmd));
    cmd.cmd = ETHTOOL_GSET;
    ifr.ifr_data = (char *)&cmd;

    if (ioctl(sock, SIOCETHTOOL, &ifr) == 0) {
        u32 speed = ethtool_cmd_speed(&cmd);
        
        /* Check for 25G speed (indicates 88x5113 retimer mode) */
        if (speed == 25000) {
            result->speed_25g = 1;
            result->retimer_mode = 1;
        } else if (speed == 10000) {
            result->retimer_mode = 1;
        }
        
        ret = 0;
    } else {
        ret = -1;
    }

    close(sock);
    return ret;
}

/* Check link status */
static int check_link_status(const char *interface, struct dpaa2_test_result *result)
{
    char path[256];
    FILE *file;
    char buffer[16];

    /* Check carrier status */
    snprintf(path, sizeof(path), "/sys/class/net/%s/carrier", interface);
    file = fopen(path, "r");
    if (file) {
        if (fgets(buffer, sizeof(buffer), file)) {
            result->link_status = atoi(buffer);
        }
        fclose(file);
    }

    return 0;
}

/* Check for 88x5113 PHY in sysfs */
static int check_88x5113_phy(const char *interface, struct dpaa2_test_result *result)
{
    char path[256];
    DIR *dir;
    struct dirent *entry;
    FILE *file;
    char buffer[256];

    /* Look for PHY information in sysfs */
    snprintf(path, sizeof(path), "/sys/class/net/%s/phydev", interface);
    
    if (readlink(path, buffer, sizeof(buffer) - 1) > 0) {
        /* Check if the PHY path contains 88x5113 information */
        if (strstr(buffer, "88x5113") || strstr(buffer, "marvell")) {
            result->phy_detected = 1;
        }
    }

    /* Also check dmesg for 88x5113 messages */
    FILE *dmesg = popen("dmesg | grep -i '88x5113' | tail -5", "r");
    if (dmesg) {
        while (fgets(buffer, sizeof(buffer), dmesg)) {
            if (strstr(buffer, interface) || strstr(buffer, "retimer")) {
                result->phy_detected = 1;
                result->retimer_mode = 1;
                break;
            }
        }
        pclose(dmesg);
    }

    return 0;
}

/* Test a single DPAA2 interface */
static int test_dpaa2_interface(const char *interface, struct dpaa2_test_result *result)
{
    memset(result, 0, sizeof(*result));
    strncpy(result->interface, interface, IFNAMSIZ - 1);

    printf("Testing interface: %s\n", interface);

    /* Get driver information */
    if (get_driver_info(interface, result) != 0) {
        printf("  ⚠ Failed to get driver information\n");
        return -1;
    }

    printf("  Driver: %s\n", result->driver_name);

    /* Check if it's DPAA2 */
    if (!is_dpaa2_interface(interface)) {
        printf("  ⚠ Not a DPAA2 interface\n");
        return -1;
    }

    printf("  ✓ DPAA2 interface detected\n");

    /* Check for 88x5113 PHY */
    check_88x5113_phy(interface, result);
    if (result->phy_detected) {
        printf("  ✓ 88x5113 PHY detected\n");
    } else {
        printf("  ⚠ 88x5113 PHY not detected\n");
    }

    /* Check PHY and speed */
    check_phy_speed(interface, result);
    if (result->speed_25g) {
        printf("  ✓ 25G speed detected (retimer mode)\n");
    } else if (result->retimer_mode) {
        printf("  ✓ Retimer mode detected\n");
    } else {
        printf("  ⚠ Retimer mode not detected\n");
    }

    /* Check link status */
    check_link_status(interface, result);
    if (result->link_status) {
        printf("  ✓ Link is UP\n");
    } else {
        printf("  ⚠ Link is DOWN\n");
    }

    return 0;
}

/* Find all DPAA2 interfaces */
static int find_dpaa2_interfaces(char interfaces[][IFNAMSIZ], int max_interfaces)
{
    DIR *dir;
    struct dirent *entry;
    int count = 0;

    dir = opendir("/sys/class/net");
    if (!dir) {
        return 0;
    }

    while ((entry = readdir(dir)) != NULL && count < max_interfaces) {
        if (strncmp(entry->d_name, ".", 1) == 0) {
            continue;
        }

        if (is_dpaa2_interface(entry->d_name)) {
            strncpy(interfaces[count], entry->d_name, IFNAMSIZ - 1);
            interfaces[count][IFNAMSIZ - 1] = '\0';
            count++;
        }
    }

    closedir(dir);
    return count;
}

/* Print test summary */
static void print_summary(struct dpaa2_test_result *results, int count)
{
    int total_phy_detected = 0;
    int total_retimer_mode = 0;
    int total_link_up = 0;
    int total_25g = 0;
    int i;

    printf("\n=== DPAA2 88x5113 Integration Test Summary ===\n");
    printf("Total DPAA2 interfaces tested: %d\n\n", count);

    for (i = 0; i < count; i++) {
        printf("Interface %s:\n", results[i].interface);
        printf("  88x5113 PHY: %s\n", results[i].phy_detected ? "Detected" : "Not detected");
        printf("  Retimer mode: %s\n", results[i].retimer_mode ? "Active" : "Inactive");
        printf("  25G speed: %s\n", results[i].speed_25g ? "Yes" : "No");
        printf("  Link status: %s\n", results[i].link_status ? "UP" : "DOWN");
        printf("  Driver: %s\n", results[i].driver_name);
        printf("\n");

        if (results[i].phy_detected) total_phy_detected++;
        if (results[i].retimer_mode) total_retimer_mode++;
        if (results[i].link_status) total_link_up++;
        if (results[i].speed_25g) total_25g++;
    }

    printf("Summary:\n");
    printf("  Interfaces with 88x5113 PHY: %d/%d\n", total_phy_detected, count);
    printf("  Interfaces in retimer mode: %d/%d\n", total_retimer_mode, count);
    printf("  Interfaces with 25G speed: %d/%d\n", total_25g, count);
    printf("  Interfaces with link UP: %d/%d\n", total_link_up, count);

    if (total_phy_detected > 0 && total_retimer_mode > 0) {
        printf("\n✓ DPAA2 88x5113 integration appears to be working!\n");
    } else {
        printf("\n✗ DPAA2 88x5113 integration may have issues.\n");
    }
}

int main(int argc, char *argv[])
{
    char interfaces[MAX_INTERFACES][IFNAMSIZ];
    struct dpaa2_test_result results[MAX_INTERFACES];
    int interface_count;
    int i;

    printf("DPAA2 Ethernet and Marvell 88x5113 PHY Integration Test\n");
    printf("=======================================================\n\n");

    /* Find DPAA2 interfaces */
    interface_count = find_dpaa2_interfaces(interfaces, MAX_INTERFACES);
    
    if (interface_count == 0) {
        printf("No DPAA2 interfaces found.\n");
        printf("Make sure DPAA2 Ethernet driver is loaded and interfaces are available.\n");
        return 1;
    }

    printf("Found %d DPAA2 interface(s)\n\n", interface_count);

    /* Test each interface */
    for (i = 0; i < interface_count; i++) {
        test_dpaa2_interface(interfaces[i], &results[i]);
        printf("\n");
    }

    /* Print summary */
    print_summary(results, interface_count);

    return 0;
}
