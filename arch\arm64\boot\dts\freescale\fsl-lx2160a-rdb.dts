// SPDX-License-Identifier: (GPL-2.0 OR MIT)
//
// Device Tree file for LX2160ARDB
//
// Copyright 2018-2020 NXP

/dts-v1/;

#include "fsl-lx2160a.dtsi"

/ {
	model = "NXP Layerscape LX2160ARDB";
	compatible = "fsl,lx2160a-rdb", "fsl,lx2160a";

	aliases {
		crypto = &crypto;
		mmc0 = &esdhc0;
		mmc1 = &esdhc1;
		serial0 = &uart0;
		ethernet0 = &dpmac17;  /* RGMII */
		ethernet1 = &dpmac18;  /* RGMII */
		ethernet2 = &dpmac3;   /* USXGMII */
		ethernet3 = &dpmac4;   /* USXGMII */
		ethernet4 = &dpmac5;   /* 88x5113 25G */
		ethernet5 = &dpmac6;   /* 88x5113 25G */
		ethernet6 = &dpmac7;   /* 88x5113 25G */
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	sb_3v3: regulator-sb3v3 {
		compatible = "regulator-fixed";
		regulator-name = "MC34717-3.3VSB";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		regulator-always-on;
	};
};

&crypto {
	status = "okay";
};

&dpmac3 {
	phy-handle = <&aquantia_phy1>;
	phy-connection-type = "usxgmii";
	managed = "in-band-status";
};

&dpmac4 {
	phy-handle = <&aquantia_phy2>;
	phy-connection-type = "usxgmii";
	managed = "in-band-status";
};

&dpmac5 {
	phy-handle = <&phy_88x5113_1>;
	phy-connection-type = "25gbase-kr";
	managed = "in-band-status";
};

&dpmac6 {
	phy-handle = <&phy_88x5113_2>;
	phy-connection-type = "25gbase-kr";
	managed = "in-band-status";
};

&dpmac7 {
	phy-handle = <&phy_88x5113_3>;
	phy-connection-type = "25gbase-kr";
	managed = "in-band-status";
};

&dpmac17 {
	phy-handle = <&rgmii_phy1>;
	phy-connection-type = "rgmii-id";
};

&dpmac18 {
	phy-handle = <&rgmii_phy2>;
	phy-connection-type = "rgmii-id";
};

&emdio1 {
	status = "okay";

	rgmii_phy1: ethernet-phy@1 {
		/* AR8035 PHY */
		compatible = "ethernet-phy-id004d.d072";
		interrupts-extended = <&extirq 4 IRQ_TYPE_LEVEL_LOW>;
		reg = <0x1>;
		eee-broken-1000t;
	};

	rgmii_phy2: ethernet-phy@2 {
		/* AR8035 PHY */
		compatible = "ethernet-phy-id004d.d072";
		interrupts-extended = <&extirq 5 IRQ_TYPE_LEVEL_LOW>;
		reg = <0x2>;
		eee-broken-1000t;
	};

	aquantia_phy1: ethernet-phy@4 {
		/* AQR107 PHY */
		compatible = "ethernet-phy-ieee802.3-c45";
		interrupts-extended = <&extirq 2 IRQ_TYPE_LEVEL_LOW>;
		reg = <0x4>;
	};

	aquantia_phy2: ethernet-phy@5 {
		/* AQR107 PHY */
		compatible = "ethernet-phy-ieee802.3-c45";
		interrupts-extended = <&extirq 3 IRQ_TYPE_LEVEL_LOW>;
		reg = <0x5>;
	};
};

&can0 {
	status = "okay";

	can-transceiver {
		max-bitrate = <5000000>;
	};
};

&can1 {
	status = "okay";

	can-transceiver {
		max-bitrate = <5000000>;
	};
};

&emdio_88x5113 {
	status = "okay";

	/* 88x5113 PHY instances - shared single physical device via I2C */
	phy_88x5113_1: ethernet-phy@1 {
		compatible = "marvell,88x5113";
		reg = <0x1>;

		/* I2C configuration for actual communication */
		marvell,i2c-bus = <1>;
		marvell,i2c-address = <0x77>;

		/* Retimer mode configuration */
		marvell,retimer-mode;
		marvell,lane-count = <4>;
		marvell,max-speed = <25000>;
		marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
		marvell,poll-interval = <2500>;

		/* Lane assignment for DPMAC5 */
		marvell,lane-assignment = <0 1>;
	};

	phy_88x5113_2: ethernet-phy@2 {
		compatible = "marvell,88x5113";
		reg = <0x2>;

		/* Same I2C configuration - shared PHY */
		marvell,i2c-bus = <1>;
		marvell,i2c-address = <0x77>;

		/* Retimer mode configuration */
		marvell,retimer-mode;
		marvell,lane-count = <4>;
		marvell,max-speed = <25000>;

		/* Lane assignment for DPMAC6 */
		marvell,lane-assignment = <2 3>;
	};

	phy_88x5113_3: ethernet-phy@3 {
		compatible = "marvell,88x5113";
		reg = <0x3>;

		/* Same I2C configuration - shared PHY */
		marvell,i2c-bus = <1>;
		marvell,i2c-address = <0x77>;

		/* Retimer mode configuration */
		marvell,retimer-mode;
		marvell,lane-count = <4>;
		marvell,max-speed = <25000>;

		/* Lane assignment for DPMAC7 - shared lanes */
		marvell,lane-assignment = <0 1 2 3>;
	};
};

&esdhc0 {
	sd-uhs-sdr104;
	sd-uhs-sdr50;
	sd-uhs-sdr25;
	sd-uhs-sdr12;
	status = "okay";
};

&esdhc1 {
	mmc-hs200-1_8v;
	mmc-hs400-1_8v;
	bus-width = <8>;
	status = "okay";
};

&fspi {
	status = "okay";

	mt35xu512aba0: flash@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "jedec,spi-nor";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
		reg = <0>;
		spi-rx-bus-width = <8>;
		spi-tx-bus-width = <8>;
	};

	mt35xu512aba1: flash@1 {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "jedec,spi-nor";
		m25p,fast-read;
		spi-max-frequency = <50000000>;
		reg = <1>;
		spi-rx-bus-width = <8>;
		spi-tx-bus-width = <8>;
	};
};

&i2c0 {
	status = "okay";

	i2c-mux@77 {
		compatible = "nxp,pca9547";
		reg = <0x77>;
		#address-cells = <1>;
		#size-cells = <0>;

		i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x2>;

			power-monitor@40 {
				compatible = "ti,ina220";
				reg = <0x40>;
				shunt-resistor = <500>;
			};
		};

		i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x3>;

			temperature-sensor@4c {
				compatible = "nxp,sa56004";
				reg = <0x4c>;
				vcc-supply = <&sb_3v3>;
			};

			temperature-sensor@4d {
				compatible = "nxp,sa56004";
				reg = <0x4d>;
				vcc-supply = <&sb_3v3>;
			};
		};
	};
};

&i2c1 {
	status = "okay";

	/* 88x5113 PHY I2C device for actual communication */
	phy_88x5113_i2c: phy@77 {
		compatible = "marvell,88x5113-i2c";
		reg = <0x77>;

		/* PHY configuration */
		marvell,device-id = <0x5113>;
		marvell,retimer-mode;
		marvell,max-lanes = <4>;
		marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;

		/* Interrupt configuration (optional) */
		interrupt-parent = <&gpio2>;
		interrupts = <10 IRQ_TYPE_LEVEL_LOW>;
		interrupt-names = "phy-interrupt";
	};
};

&i2c4 {
	status = "okay";

	rtc@51 {
		compatible = "nxp,pcf2129";
		reg = <0x51>;
		/* IRQ_RTC_B -> IRQ08, active low */
		interrupts-extended = <&extirq 8 IRQ_TYPE_LEVEL_LOW>;
	};
};

&optee {
	status = "okay";
};

&pcs_mdio3 {
	status = "okay";
};

&pcs_mdio4 {
	status = "okay";
};

&sata0 {
	status = "okay";
};

&sata1 {
	status = "okay";
};

&sata2 {
	status = "okay";
};

&sata3 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&usb0 {
	status = "okay";
};

&usb1 {
	status = "okay";
};
