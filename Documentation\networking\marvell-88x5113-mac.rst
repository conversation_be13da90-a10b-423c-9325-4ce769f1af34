.. SPDX-License-Identifier: GPL-2.0

=================================
Marvell 88x5113 MAC Driver
=================================

Overview
========

The Marvell 88x5113 MAC driver provides Ethernet MAC functionality for systems
using the Marvell 88x5113 PHY in retimer mode. This driver is specifically
designed to integrate with the 88x5113 PHY driver and supports high-speed
Ethernet operation up to 25G.

Key Features
============

* Integration with 88x5113 PHY driver via phylink
* Support for retimer mode operation (10G and 25G)
* DMA-based packet processing with scatter-gather support
* NAPI-based interrupt handling for improved performance
* Automatic link monitoring and recovery
* Device tree configuration support
* Standard Linux networking framework integration

Architecture
============

The driver follows the standard Linux networking architecture:

::

    ┌─────────────────────────────────────────┐
    │           Network Stack                 │
    ├─────────────────────────────────────────┤
    │         netdev Interface                │
    ├─────────────────────────────────────────┤
    │       88x5113 MAC Driver               │
    │  ┌─────────────┐  ┌─────────────────┐   │
    │  │ TX/RX DMA   │  │ Phylink         │   │
    │  │ Engine      │  │ Integration     │   │
    │  └─────────────┘  └─────────────────┘   │
    │  ┌─────────────────────────────────────┐ │
    │  │        Hardware Abstraction        │ │
    │  └─────────────────────────────────────┘ │
    ├─────────────────────────────────────────┤
    │           88x5113 PHY Driver            │
    ├─────────────────────────────────────────┤
    │              I2C Subsystem              │
    └─────────────────────────────────────────┘

Driver Components
=================

**DMA Engine**
  - Separate TX and RX descriptor rings
  - Scatter-gather support for efficient memory usage
  - Automatic buffer management and recycling

**Phylink Integration**
  - Seamless integration with 88x5113 PHY driver
  - Automatic speed and duplex negotiation
  - Link state monitoring and reporting

**NAPI Support**
  - Interrupt-driven packet processing
  - Efficient CPU utilization under high load
  - Configurable polling weights

**Hardware Abstraction**
  - Register access abstraction
  - Clock and reset management
  - Platform device integration

Device Tree Configuration
=========================

Basic Configuration
-------------------

::

    ethernet@1c30000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c30000 0x10000>;
        interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
        
        clocks = <&gcc GCC_ETH_MAC_CLK>;
        clock-names = "mac";
        
        phy-handle = <&phy0>;
        phy-mode = "25gbase-kr";
        
        marvell,retimer-mode;
        marvell,max-speed = <25000>;
    };

Multiple MAC Configuration
---------------------------

For systems with multiple 88x5113 PHYs, configure multiple MAC instances:

::

    ethernet@1c30000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c30000 0x10000>;
        interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&gcc GCC_ETH_MAC1_CLK>;
        clock-names = "mac";
        phy-handle = <&phy0>;
        phy-mode = "25gbase-kr";
        marvell,retimer-mode;
    };

    ethernet@1c40000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c40000 0x10000>;
        interrupts = <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&gcc GCC_ETH_MAC2_CLK>;
        clock-names = "mac";
        phy-handle = <&phy1>;
        phy-mode = "25gbase-kr";
        marvell,retimer-mode;
    };

    ethernet@1c50000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c50000 0x10000>;
        interrupts = <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&gcc GCC_ETH_MAC3_CLK>;
        clock-names = "mac";
        phy-handle = <&phy2>;
        phy-mode = "25gbase-kr";
        marvell,retimer-mode;
    };

Required Properties
===================

* ``compatible``: Must be "marvell,88x5113-mac"
* ``reg``: MAC controller register base and size
* ``interrupts``: MAC controller interrupt
* ``clocks``: MAC clock reference
* ``clock-names``: Must include "mac"

Optional Properties
===================

* ``phy-handle``: Phandle to connected PHY
* ``phy-mode``: PHY interface mode (25gbase-kr, 10gbase-kr, etc.)
* ``marvell,retimer-mode``: Enable retimer mode operation
* ``marvell,max-speed``: Maximum speed in Mbps (1000, 10000, 25000)
* ``resets``: MAC reset control
* ``local-mac-address``: MAC address assignment

Driver Operation
================

Initialization Sequence
------------------------

1. Platform device probe and resource allocation
2. Clock and reset initialization
3. Phylink setup and PHY connection
4. DMA ring allocation and configuration
5. Interrupt handler registration
6. Network device registration

Runtime Operation
------------------

1. **Link Monitoring**: Continuous monitoring via phylink and work queue
2. **Packet Transmission**: DMA-based with descriptor rings
3. **Packet Reception**: NAPI-based polling with buffer recycling
4. **Error Handling**: Automatic recovery and statistics reporting

Performance Optimization
=========================

The driver includes several optimizations for high-speed operation:

* **NAPI Processing**: Reduces interrupt overhead
* **DMA Coherent Buffers**: Minimizes cache operations
* **Scatter-Gather**: Efficient memory utilization
* **Ring Buffer Management**: Optimized descriptor handling

Supported Speeds
================

* **25G**: Primary retimer mode (25000 Mbps)
* **10G**: Secondary retimer mode (10000 Mbps)
* **1G**: Fallback mode (1000 Mbps)

The driver automatically configures the appropriate speed based on PHY
capabilities and device tree configuration.

Testing
=======

A comprehensive test suite is provided:

::

    # Compile the test
    gcc -o marvell_88x5113_mac_test tools/testing/selftests/net/marvell_88x5113_mac_test.c

    # Run tests (default interface: eth0)
    ./marvell_88x5113_mac_test

    # Test specific interface
    ./marvell_88x5113_mac_test eth1

The test suite verifies:

* Driver detection and loading
* PHY integration and communication
* Link status monitoring
* Interface configuration
* Statistics collection

Troubleshooting
===============

Common Issues
-------------

1. **Driver Not Loading**
   - Verify CONFIG_MARVELL_88X5113_MAC=y/m
   - Check device tree configuration
   - Ensure required clocks are available

2. **No Link Detected**
   - Verify PHY driver is loaded
   - Check I2C communication with PHY
   - Monitor PHY lane status

3. **Performance Issues**
   - Check interrupt configuration
   - Verify DMA coherency settings
   - Monitor NAPI performance

Debug Information
-----------------

Enable debug output::

    echo 8 > /proc/sys/kernel/printk
    dmesg | grep 88x5113

Monitor interface status::

    cat /sys/class/net/eth0/operstate
    cat /sys/class/net/eth0/carrier
    ethtool eth0

Check statistics::

    cat /proc/net/dev
    ethtool -S eth0

Integration with 88x5113 PHY
=============================

The MAC driver is designed to work seamlessly with the 88x5113 PHY driver:

* **Phylink Integration**: Automatic speed and duplex configuration
* **Link State Synchronization**: Real-time link status updates
* **Retimer Mode Support**: Optimized for retimer operation
* **Multi-Lane Support**: Handles multiple PHY lanes

The PHY driver handles the I2C communication and lane management, while
the MAC driver focuses on packet processing and network integration.

References
==========

* Marvell 88x5113 MAC controller datasheet
* Linux phylink documentation
* Linux networking driver development guide
* Device tree binding documentation
