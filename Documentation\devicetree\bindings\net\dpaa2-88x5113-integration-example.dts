// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree example for DPAA2 Ethernet integration with Marvell 88x5113 PHY
 *
 * This example shows how to configure DPAA2 Ethernet with 3 MAC controllers
 * connected to a single 88x5113 PHY in retimer mode, replacing the Inphi PHY.
 */

/dts-v1/;

/ {
    model = "DPAA2 88x5113 Integration Example";
    compatible = "fsl,dpaa2-88x5113-example";

    #address-cells = <2>;
    #size-cells = <2>;

    /* I2C bus for 88x5113 PHY communication */
    i2c1: i2c@2000000 {
        compatible = "fsl,vf610-i2c";
        reg = <0x0 0x2000000 0x0 0x10000>;
        interrupts = <0 34 4>;
        clocks = <&clockgen 4 1>;
        clock-frequency = <400000>;
        #address-cells = <1>;
        #size-cells = <0>;

        /* 88x5113 PHY I2C device */
        phy_88x5113_i2c: phy@77 {
            compatible = "marvell,88x5113-i2c";
            reg = <0x77>;
        };
    };

    /* MDIO bus for PHY registration */
    emdio1: mdio@8b96000 {
        compatible = "fsl,fman-memac-mdio", "fsl,fman-xmdio";
        reg = <0x0 0x8b96000 0x0 0x1000>;
        interrupts = <0 90 4>;
        #address-cells = <1>;
        #size-cells = <0>;

        /* 88x5113 PHY - MDIO registration but I2C communication */
        phy1: phy@1 {
            compatible = "marvell,88x5113";
            reg = <1>;
            
            /* I2C configuration for actual communication */
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            
            /* Retimer mode configuration */
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
            marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
            marvell,poll-interval = <2500>;
        };

        phy2: phy@2 {
            compatible = "marvell,88x5113";
            reg = <2>;
            
            /* Same I2C configuration - shared PHY */
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            
            /* Retimer mode configuration */
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
        };

        phy3: phy@3 {
            compatible = "marvell,88x5113";
            reg = <3>;
            
            /* Same I2C configuration - shared PHY */
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            
            /* Retimer mode configuration */
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
        };
    };

    /* DPAA2 Management Complex */
    fsl_mc: fsl-mc@80c000000 {
        compatible = "fsl,qoriq-mc";
        reg = <0x00000008 0x0c000000 0 0x40>,    /* MC portal base */
              <0x00000000 0x08340000 0 0x40000>; /* MC control reg */
        msi-parent = <&its>;
        iommu-parent = <&smmu>;
        #address-cells = <3>;
        #size-cells = <1>;

        /*
         * Region type 0x0 - MC portals
         * Region type 0x1 - QBMAN portals
         */
        ranges = <0x0 0x0 0x0 0x8 0x0c000000 0x4000000
                  0x1 0x0 0x0 0x8 0x18000000 0x8000000>;

        /* DPAA2 MAC objects */
        dpmacs {
            #address-cells = <1>;
            #size-cells = <0>;

            /* DPMAC 1 - Connected to 88x5113 PHY */
            dpmac@1 {
                compatible = "fsl,qoriq-mc-dpmac";
                reg = <1>;
                phy-handle = <&phy1>;
                phy-connection-type = "25gbase-kr";
                
                /* 88x5113 specific properties */
                marvell,88x5113-retimer-mode;
                marvell,max-speed = <25000>;
            };

            /* DPMAC 2 - Connected to same 88x5113 PHY */
            dpmac@2 {
                compatible = "fsl,qoriq-mc-dpmac";
                reg = <2>;
                phy-handle = <&phy2>;
                phy-connection-type = "25gbase-kr";
                
                /* 88x5113 specific properties */
                marvell,88x5113-retimer-mode;
                marvell,max-speed = <25000>;
            };

            /* DPMAC 3 - Connected to same 88x5113 PHY */
            dpmac@3 {
                compatible = "fsl,qoriq-mc-dpmac";
                reg = <3>;
                phy-handle = <&phy3>;
                phy-connection-type = "25gbase-kr";
                
                /* 88x5113 specific properties */
                marvell,88x5113-retimer-mode;
                marvell,max-speed = <25000>;
            };
        };

        /* DPAA2 Ethernet objects */
        ethernet@1 {
            compatible = "fsl,qoriq-mc-dpni";
            reg = <1>;
            
            /* Link to DPMAC 1 */
            fsl,qoriq-mc-connections = <&dpmac@1>;
        };

        ethernet@2 {
            compatible = "fsl,qoriq-mc-dpni";
            reg = <2>;
            
            /* Link to DPMAC 2 */
            fsl,qoriq-mc-connections = <&dpmac@2>;
        };

        ethernet@3 {
            compatible = "fsl,qoriq-mc-dpni";
            reg = <3>;
            
            /* Link to DPMAC 3 */
            fsl,qoriq-mc-connections = <&dpmac@3>;
        };
    };

    /* Memory configuration */
    memory@80000000 {
        device_type = "memory";
        reg = <0x00000000 0x80000000 0 0x80000000>,
              <0x00000008 0x80000000 0 0x80000000>;
    };

    /* Interrupt controller */
    gic: interrupt-controller@6000000 {
        compatible = "arm,gic-v3";
        #interrupt-cells = <3>;
        interrupt-controller;
        reg = <0x0 0x06000000 0 0x10000>, /* GIC Dist */
              <0x0 0x06200000 0 0x200000>, /* GICR (RD_base + SGI_base) */
              <0x0 0x0c0c0000 0 0x2000>,   /* GICC */
              <0x0 0x0c0d0000 0 0x1000>,   /* GICH */
              <0x0 0x0c0e0000 0 0x20000>;  /* GICV */
        interrupts = <1 9 4>;
    };

    /* ITS for MSI */
    its: gic-its@6020000 {
        compatible = "arm,gic-v3-its";
        msi-controller;
        reg = <0x0 0x6020000 0 0x20000>;
    };

    /* SMMU for IOMMU */
    smmu: iommu@5000000 {
        compatible = "arm,mmu-500";
        reg = <0 0x5000000 0 0x800000>;
        #global-interrupts = <12>;
        interrupts = <0 13 4>, <0 14 4>, <0 15 4>, <0 16 4>,
                     <0 17 4>, <0 18 4>, <0 19 4>, <0 20 4>,
                     <0 21 4>, <0 22 4>, <0 23 4>, <0 24 4>,
                     <0 25 4>, <0 26 4>, <0 27 4>, <0 28 4>,
                     <0 29 4>, <0 30 4>, <0 31 4>, <0 32 4>;
        #iommu-cells = <1>;
    };

    /* Clock generator */
    clockgen: clocking@1300000 {
        compatible = "fsl,ls2080a-clockgen";
        reg = <0 0x1300000 0 0xa0000>;
        #clock-cells = <2>;
        clocks = <&sysclk>;
    };

    /* System clock */
    sysclk: sysclk {
        compatible = "fixed-clock";
        #clock-cells = <0>;
        clock-frequency = <100000000>;
        clock-output-names = "sysclk";
    };

    /* Aliases for network interfaces */
    aliases {
        ethernet0 = "/fsl-mc/ethernet@1";
        ethernet1 = "/fsl-mc/ethernet@2";
        ethernet2 = "/fsl-mc/ethernet@3";
    };

    /* Chosen configuration */
    chosen {
        bootargs = "console=ttyS0,115200 root=/dev/ram0 rw";
        stdout-path = "serial0:115200n8";
    };
};

/*
 * Integration Notes:
 * 
 * 1. Single 88x5113 PHY serves three DPAA2 MAC controllers (DPMAC 1-3)
 * 2. PHY uses I2C communication (address 0x77 on bus 1) instead of MDIO
 * 3. All DPMACs operate in 25G retimer mode
 * 4. DPAA2 Ethernet driver will detect 88x5113 PHY and use integration functions
 * 5. Replaces previous Inphi PHY integration in DPAA2
 * 
 * Expected Network Interfaces:
 * - eth0: First DPNI connected to DPMAC 1 (88x5113 lane 0-1)
 * - eth1: Second DPNI connected to DPMAC 2 (88x5113 lane 2-3)  
 * - eth2: Third DPNI connected to DPMAC 3 (88x5113 shared lanes)
 * 
 * All interfaces will show 25G speed when link is established
 * through the 88x5113 retimer.
 */
