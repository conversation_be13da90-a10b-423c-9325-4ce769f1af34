# SPDX-License-Identifier: GPL-2.0-only
#
# Marvell device configuration
#

config NET_VENDOR_MARVELL
	bool "Marvell devices"
	default y
	depends on PCI || CPU_PXA168 || PPC32 || PLAT_ORION || INET || COMPILE_TEST
	help
	  If you have a network (Ethernet) card belonging to this class, say Y.

	  Note that the answer to this question doesn't directly affect the
	  kernel: saying N will just cause the configurator to skip all
	  the questions about Marvell devices. If you say Y, you will be
	  asked for your specific card in the following questions.

if NET_VENDOR_MARVELL

config MV643XX_ETH
	tristate "Marvell Discovery (643XX) and Orion ethernet support"
	depends on PPC32 || PLAT_ORION || COMPILE_TEST
	depends on INET
	select PHY<PERSON><PERSON>
	select MVMDIO
	help
	  This driver supports the gigabit ethernet MACs in the
	  Marvell Discovery PPC/MIPS chipset family (MV643XX) and
	  in the Marvell Orion ARM SoC family.

	  Some boards that use the Discovery chipset are the Momenco
	  Ocelot C and Jaguar ATX and Pegasos II.

config MV<PERSON><PERSON>
	tristate "Marvell MDIO interface support"
	depends on HAS_IOMEM
	select PHY<PERSON><PERSON>
	help
	  This driver supports the MDIO interface found in the network
	  interface units of the Marvell EBU SoCs (Kirkwood, Orion5x,
	  Dove, Armada 370 and Armada XP).

	  This driver is used by the MV643XX_ETH and MVNETA drivers.

config MVNETA_BM_ENABLE
	tristate "Marvell Armada 38x/XP network interface BM support"
	depends on MVNETA
	depends on !64BIT
	help
	  This driver supports auxiliary block of the network
	  interface units in the Marvell ARMADA XP and ARMADA 38x SoC
	  family, which is called buffer manager.

	  This driver, when enabled, strictly cooperates with mvneta
	  driver and is common for all network ports of the devices,
	  even for Armada 370 SoC, which doesn't support hardware
	  buffer management.

config MVNETA
	tristate "Marvell Armada 370/38x/XP/37xx network interface support"
	depends on ARCH_MVEBU || COMPILE_TEST
	select MVMDIO
	select PHYLINK
	select PAGE_POOL
	help
	  This driver supports the network interface units in the
	  Marvell ARMADA XP, ARMADA 370, ARMADA 38x and
	  ARMADA 37xx SoC family.

	  Note that this driver is distinct from the mv643xx_eth
	  driver, which should be used for the older Marvell SoCs
	  (Dove, Orion, Discovery, Kirkwood).

config MVNETA_BM
	tristate
	depends on !64BIT
	default y if MVNETA=y && MVNETA_BM_ENABLE!=n
	default MVNETA_BM_ENABLE
	select HWBM
	select GENERIC_ALLOCATOR
	help
	  MVNETA_BM must not be 'm' if MVNETA=y, so this symbol ensures
	  that all dependencies are met.

config MVPP2
	tristate "Marvell Armada 375/7K/8K network interface support"
	depends on ARCH_MVEBU || COMPILE_TEST
	select MVMDIO
	select PHYLINK
	select PAGE_POOL
	help
	  This driver supports the network interface units in the
	  Marvell ARMADA 375, 7K and 8K SoCs.

config MVPP2_PTP
	bool "Marvell Armada 8K Enable PTP support"
	depends on (PTP_1588_CLOCK = y && MVPP2 = y) || \
		   (PTP_1588_CLOCK && MVPP2 = m)

config PXA168_ETH
	tristate "Marvell pxa168 ethernet support"
	depends on HAS_IOMEM
	depends on CPU_PXA168 || ARCH_BERLIN || COMPILE_TEST
	select PHYLIB
	help
	  This driver supports the pxa168 Ethernet ports.

	  To compile this driver as a module, choose M here. The module
	  will be called pxa168_eth.

config SKGE
	tristate "Marvell Yukon Gigabit Ethernet support"
	depends on PCI
	select CRC32
	help
	  This driver support the Marvell Yukon or SysKonnect SK-98xx/SK-95xx
	  and related Gigabit Ethernet adapters. It is a new smaller driver
	  with better performance and more complete ethtool support.

	  It does not support the link failover and network management
	  features that "portable" vendor supplied sk98lin driver does.

	  This driver supports adapters based on the original Yukon chipset:
	  Marvell 88E8001, Belkin F5D5005, CNet GigaCard, DLink DGE-530T,
	  Linksys EG1032/EG1064, 3Com 3C940/3C940B, SysKonnect SK-9871/9872.

	  It does not support the newer Yukon2 chipset: a separate driver,
	  sky2, is provided for these adapters.

	  To compile this driver as a module, choose M here: the module
	  will be called skge.  This is recommended.

config SKGE_DEBUG
	bool "Debugging interface"
	depends on SKGE && DEBUG_FS
	help
	  This option adds the ability to dump driver state for debugging.
	  The file /sys/kernel/debug/skge/ethX displays the state of the internal
	  transmit and receive rings.

	  If unsure, say N.

config SKGE_GENESIS
	bool "Support for older SysKonnect Genesis boards"
	depends on SKGE
	help
	 This enables support for the older and uncommon SysKonnect Genesis
	 chips, which support MII via an external transceiver, instead of
	 an internal one. Disabling this option will save some memory
	 by making code smaller. If unsure say Y.

config SKY2
	tristate "Marvell Yukon 2 support"
	depends on PCI
	select CRC32
	help
	  This driver supports Gigabit Ethernet adapters based on the
	  Marvell Yukon 2 chipset:
	  Marvell 88E8021/88E8022/88E8035/88E8036/88E8038/88E8050/88E8052/
	  88E8053/88E8055/88E8061/88E8062, SysKonnect SK-9E21D/SK-9S21

	  There is companion driver for the older Marvell Yukon and
	  SysKonnect Genesis based adapters: skge.

	  To compile this driver as a module, choose M here: the module
	  will be called sky2.  This is recommended.

config SKY2_DEBUG
	bool "Debugging interface"
	depends on SKY2 && DEBUG_FS
	help
	  This option adds the ability to dump driver state for debugging.
	  The file /sys/kernel/debug/sky2/ethX displays the state of the internal
	  transmit and receive rings.

	  If unsure, say N.

config MARVELL_88X5113_MAC
	tristate "Marvell 88x5113 MAC support"
	depends on HAS_IOMEM && HAS_DMA
	select PHYLINK
	select CRC32
	help
	  This driver supports the Marvell 88x5113 MAC controller for
	  high-speed Ethernet applications. It is designed to work with
	  the Marvell 88x5113 PHY in retimer mode and supports speeds
	  up to 25G.

	  The driver integrates with the 88x5113 PHY driver and provides
	  standard Ethernet MAC functionality including DMA-based packet
	  processing, NAPI support, and phylink integration.

	  To compile this driver as a module, choose M here: the module
	  will be called marvell_88x5113_mac.

source "drivers/net/ethernet/marvell/octeontx2/Kconfig"
source "drivers/net/ethernet/marvell/prestera/Kconfig"

endif # NET_VENDOR_MARVELL
