#!/bin/bash
# SPDX-License-Identifier: GPL-2.0
#
# Test script for LX2160A 88x5113 PHY device tree configuration
# This script validates the device tree configuration for the integration

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
KERNEL_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"
DTS_FILE="${KERNEL_DIR}/arch/arm64/boot/dts/freescale/fsl-lx2160a-rdb.dts"
DTSI_FILE="${KERNEL_DIR}/arch/arm64/boot/dts/freescale/fsl-lx2160a.dtsi"

echo "LX2160A 88x5113 PHY Device Tree Configuration Test"
echo "=================================================="
echo

# Check if device tree files exist
echo "1. Checking device tree files..."
if [ ! -f "$DTS_FILE" ]; then
    echo "ERROR: $DTS_FILE not found"
    exit 1
fi

if [ ! -f "$DTSI_FILE" ]; then
    echo "ERROR: $DTSI_FILE not found"
    exit 1
fi

echo "✓ Device tree files found"
echo

# Check for 88x5113 PHY configuration
echo "2. Checking 88x5113 PHY configuration..."

# Check for 88x5113 compatible strings
if grep -q "marvell,88x5113" "$DTS_FILE"; then
    echo "✓ 88x5113 PHY compatible string found"
else
    echo "✗ 88x5113 PHY compatible string not found"
    exit 1
fi

# Check for I2C configuration
if grep -q "marvell,i2c-address.*0x77" "$DTS_FILE"; then
    echo "✓ I2C address configuration found"
else
    echo "✗ I2C address configuration not found"
    exit 1
fi

# Check for retimer mode
if grep -q "marvell,retimer-mode" "$DTS_FILE"; then
    echo "✓ Retimer mode configuration found"
else
    echo "✗ Retimer mode configuration not found"
    exit 1
fi

echo

# Check DPMAC configuration
echo "3. Checking DPMAC configuration..."

# Check for DPMAC5 configuration
if grep -q "dpmac5" "$DTS_FILE" && grep -A 5 "dpmac5" "$DTS_FILE" | grep -q "phy_88x5113_1"; then
    echo "✓ DPMAC5 configured for 88x5113"
else
    echo "✗ DPMAC5 not properly configured for 88x5113"
    exit 1
fi

# Check for DPMAC6 configuration
if grep -q "dpmac6" "$DTS_FILE" && grep -A 5 "dpmac6" "$DTS_FILE" | grep -q "phy_88x5113_2"; then
    echo "✓ DPMAC6 configured for 88x5113"
else
    echo "✗ DPMAC6 not properly configured for 88x5113"
    exit 1
fi

# Check for DPMAC7 configuration
if grep -q "dpmac7" "$DTS_FILE" && grep -A 5 "dpmac7" "$DTS_FILE" | grep -q "phy_88x5113_3"; then
    echo "✓ DPMAC7 configured for 88x5113"
else
    echo "✗ DPMAC7 not properly configured for 88x5113"
    exit 1
fi

# Check for 25gbase-kr interface type
if grep -q "25gbase-kr" "$DTS_FILE"; then
    echo "✓ 25GBASE-KR interface type configured"
else
    echo "✗ 25GBASE-KR interface type not found"
    exit 1
fi

echo

# Check I2C bus configuration
echo "4. Checking I2C bus configuration..."

# Check for I2C1 configuration
if grep -q "&i2c1" "$DTS_FILE"; then
    echo "✓ I2C1 bus configuration found"
else
    echo "✗ I2C1 bus configuration not found"
    exit 1
fi

# Check for I2C device at 0x77
if grep -A 10 "&i2c1" "$DTS_FILE" | grep -q "phy@77"; then
    echo "✓ I2C device at 0x77 configured"
else
    echo "✗ I2C device at 0x77 not found"
    exit 1
fi

echo

# Check MDIO bus configuration
echo "5. Checking MDIO bus configuration..."

# Check for emdio_88x5113 in dtsi
if grep -q "emdio_88x5113" "$DTSI_FILE"; then
    echo "✓ MDIO bus for 88x5113 found in dtsi"
else
    echo "✗ MDIO bus for 88x5113 not found in dtsi"
    exit 1
fi

# Check for emdio_88x5113 usage in dts
if grep -q "&emdio_88x5113" "$DTS_FILE"; then
    echo "✓ MDIO bus for 88x5113 used in dts"
else
    echo "✗ MDIO bus for 88x5113 not used in dts"
    exit 1
fi

echo

# Check aliases configuration
echo "6. Checking network interface aliases..."

if grep -A 10 "aliases" "$DTS_FILE" | grep -q "ethernet.*dpmac5"; then
    echo "✓ Ethernet alias for DPMAC5 found"
else
    echo "✗ Ethernet alias for DPMAC5 not found"
    exit 1
fi

if grep -A 10 "aliases" "$DTS_FILE" | grep -q "ethernet.*dpmac6"; then
    echo "✓ Ethernet alias for DPMAC6 found"
else
    echo "✗ Ethernet alias for DPMAC6 not found"
    exit 1
fi

if grep -A 10 "aliases" "$DTS_FILE" | grep -q "ethernet.*dpmac7"; then
    echo "✓ Ethernet alias for DPMAC7 found"
else
    echo "✗ Ethernet alias for DPMAC7 not found"
    exit 1
fi

echo

# Check for removal of Inphi PHY
echo "7. Checking Inphi PHY removal..."

if ! grep -q "inphi_phy" "$DTS_FILE"; then
    echo "✓ Inphi PHY references removed"
else
    echo "⚠ Inphi PHY references still present (may be intentional)"
fi

echo

# Try to compile device tree if dtc is available
echo "8. Testing device tree compilation..."

if command -v dtc >/dev/null 2>&1; then
    echo "Attempting to compile device tree..."
    
    # Create temporary directory
    TEMP_DIR=$(mktemp -d)
    
    # Copy include files
    cp -r "${KERNEL_DIR}/include/dt-bindings" "${TEMP_DIR}/"
    
    # Try to compile
    if dtc -I dts -O dtb -i "${TEMP_DIR}" -i "${KERNEL_DIR}/arch/arm64/boot/dts/freescale" \
           "$DTS_FILE" -o "${TEMP_DIR}/test.dtb" 2>/dev/null; then
        echo "✓ Device tree compiles successfully"
        
        # Check if 88x5113 nodes are present in compiled DTB
        if dtc -I dtb -O dts "${TEMP_DIR}/test.dtb" 2>/dev/null | grep -q "88x5113"; then
            echo "✓ 88x5113 nodes present in compiled DTB"
        else
            echo "✗ 88x5113 nodes not found in compiled DTB"
        fi
    else
        echo "✗ Device tree compilation failed"
        echo "Run manually: dtc -I dts -O dtb $DTS_FILE"
    fi
    
    # Cleanup
    rm -rf "$TEMP_DIR"
else
    echo "⚠ dtc not available, skipping compilation test"
fi

echo

# Summary
echo "9. Configuration Summary"
echo "========================"

echo "Device Tree Files:"
echo "  - Main DTS: $DTS_FILE"
echo "  - Base DTSI: $DTSI_FILE"
echo

echo "88x5113 PHY Configuration:"
echo "  - Compatible: marvell,88x5113"
echo "  - I2C Bus: 1"
echo "  - I2C Address: 0x77"
echo "  - Mode: Retimer"
echo "  - Max Speed: 25G"
echo

echo "DPMAC Assignments:"
echo "  - DPMAC5 -> phy_88x5113_1 (eth4)"
echo "  - DPMAC6 -> phy_88x5113_2 (eth5)"
echo "  - DPMAC7 -> phy_88x5113_3 (eth6)"
echo

echo "Interface Types:"
echo "  - All interfaces: 25gbase-kr"
echo

echo "✓ LX2160A 88x5113 PHY device tree configuration appears correct!"
echo

echo "Next Steps:"
echo "1. Build kernel with updated device tree"
echo "2. Enable CONFIG_MARVELL_88X5113_PHY=y"
echo "3. Boot with new device tree"
echo "4. Run integration test: tools/testing/selftests/net/dpaa2_88x5113_integration_test"
echo "5. Verify interfaces: ip link show eth4 eth5 eth6"
echo "6. Check speeds: ethtool eth4 eth5 eth6"
