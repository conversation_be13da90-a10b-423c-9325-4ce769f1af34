// SPDX-License-Identifier: GPL-2.0+
/*
 * Marvell 88x5113 PHY driver
 *
 * Based on the mxd API and inphi.c driver
 * Supports retimer mode operation with I2C communication
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/phy.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/of.h>
#include <linux/workqueue.h>
#include <linux/firmware.h>
#include <linux/crc32.h>

/* PHY ID for 88x5113 */
#define PHY_ID_88X5113         0x02B09AA0
#define PHY_ID_MASK            0xFFFFFFF0

/* I2C address for 88x5113 */
#define MXD_I2C_ADDR           0x77

/* MMD definitions */
#define MDIO_MMD_VEND1         0x1E
#define MDIO_MMD_VEND2         0x1F
#define MXD_LINE_SIDE          3
#define MXD_HOST_SIDE          4
#define MXD_CHIP_REG           31

/* Poll delay in milliseconds */
#define MXD_POLL_DELAY         2500

/* Lane definitions */
#define ALL_LANES              4
#define MAX_LANES              4

/* MXD API status codes */
#define MXD_OK                 0
#define MXD_FAIL               1

/* Retimer mode definitions */
#define MXD_MODE_RETIMER       0x1000
#define MXD_RETIMER_25G        0x1001
#define MXD_RETIMER_10G        0x1002

/* Register definitions for 88x5113 */
#define MXD_DEVICE_ID_REG      0x0002
#define MXD_DEVICE_REV_REG     0x0003
#define MXD_LANE_STATUS_REG    0x0123  /* Base register, add lane offset */
#define MXD_LANE_CTRL_REG      0x0120  /* Base register, add lane offset */
#define MXD_VCO_CODE_REG       0x019D  /* Base register, add lane offset */

/* Lane status bits */
#define MXD_LANE_LOCK_BIT      15
#define MXD_LANE_READY_BIT     14

/* MXD API types */
typedef unsigned char  MXD_U8;
typedef unsigned short MXD_U16;
typedef unsigned int   MXD_U32;
typedef int            MXD_STATUS;
typedef void*          MXD_PVOID;
typedef int            MXD_BOOL;
typedef void*          MXD_DEV_PTR;

/* Function pointer types for MDIO operations */
typedef MXD_STATUS (*FMXD_READ_MDIO)(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                                     MXD_U16 mmd, MXD_U16 reg, MXD_U16* value);
typedef MXD_STATUS (*FMXD_WRITE_MDIO)(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                                      MXD_U16 mmd, MXD_U16 reg, MXD_U16 value);

/* MXD device structure (simplified version of MXD_DEV) */
struct mxd_dev {
    MXD_U16 deviceId;
    MXD_U16 chipRevision;
    MXD_U16 mdioPort;
    MXD_U16 portCount;
    MXD_BOOL devEnabled;
    FMXD_READ_MDIO fmxdReadMdio;
    FMXD_WRITE_MDIO fmxdWriteMdio;
    MXD_PVOID hostContext;
};

/* Driver private data structure */
struct mxd_priv {
    struct i2c_client *client;
    struct phy_device *phydev;
    struct workqueue_struct *wq;
    struct delayed_work mxd_work;
    unsigned long onesec;
    int vco_codes[MAX_LANES];
    struct mxd_dev mxd_device;
    void *host_context;
    bool retimer_mode;
    u16 lane_status[MAX_LANES];
    u16 device_id;
    u16 revision;
};

static struct mxd_priv *mxd_private;

/* Forward declarations */
static MXD_STATUS mxd_i2c_read_mdio(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                                   MXD_U16 mmd, MXD_U16 reg, MXD_U16* value);
static MXD_STATUS mxd_i2c_write_mdio(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                                    MXD_U16 mmd, MXD_U16 reg, MXD_U16 value);
static void mxd_work_handler(struct work_struct *work);
static int mxd_lane_recovery(int lane);
static int mxd_init_driver(struct mxd_priv *priv);

/* I2C-based MDIO read function with retry mechanism */
static MXD_STATUS mxd_i2c_read_mdio(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                                   MXD_U16 mmd, MXD_U16 reg, MXD_U16* value)
{
    struct mxd_priv *priv = (struct mxd_priv *)pDev;
    struct i2c_client *client = priv->client;
    struct i2c_msg msgs[2];
    u8 addr_buf[4];
    u8 data_buf[2];
    int ret, retry;

    if (!client || !value) {
        return MXD_FAIL;
    }

    /* Format the register address for I2C transaction */
    addr_buf[0] = (mmd >> 8) & 0xFF;
    addr_buf[1] = mmd & 0xFF;
    addr_buf[2] = (reg >> 8) & 0xFF;
    addr_buf[3] = reg & 0xFF;

    /* Setup I2C messages for write-then-read transaction */
    msgs[0].addr = client->addr;
    msgs[0].flags = 0;  /* Write */
    msgs[0].len = 4;
    msgs[0].buf = addr_buf;

    msgs[1].addr = client->addr;
    msgs[1].flags = I2C_M_RD;  /* Read */
    msgs[1].len = 2;
    msgs[1].buf = data_buf;

    /* Retry mechanism for I2C communication */
    for (retry = 0; retry < 3; retry++) {
        ret = i2c_transfer(client->adapter, msgs, 2);
        if (ret == 2) {
            *value = (data_buf[0] << 8) | data_buf[1];
            return MXD_OK;
        }

        if (retry < 2) {
            usleep_range(100, 200);  /* Small delay before retry */
        }
    }

    dev_err(&client->dev, "I2C read failed after retries: mmd=0x%x, reg=0x%x, ret=%d\n",
            mmd, reg, ret);
    return MXD_FAIL;
}

/* I2C-based MDIO write function with retry mechanism */
static MXD_STATUS mxd_i2c_write_mdio(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                                    MXD_U16 mmd, MXD_U16 reg, MXD_U16 value)
{
    struct mxd_priv *priv = (struct mxd_priv *)pDev;
    struct i2c_client *client = priv->client;
    u8 buf[6];
    int ret, retry;

    if (!client) {
        return MXD_FAIL;
    }

    /* Format the command for I2C transaction */
    buf[0] = (mmd >> 8) & 0xFF;
    buf[1] = mmd & 0xFF;
    buf[2] = (reg >> 8) & 0xFF;
    buf[3] = reg & 0xFF;
    buf[4] = (value >> 8) & 0xFF;
    buf[5] = value & 0xFF;

    /* Retry mechanism for I2C communication */
    for (retry = 0; retry < 3; retry++) {
        ret = i2c_master_send(client, buf, 6);
        if (ret == 6) {
            return MXD_OK;
        }

        if (retry < 2) {
            usleep_range(100, 200);  /* Small delay before retry */
        }
    }

    dev_err(&client->dev, "I2C write failed after retries: mmd=0x%x, reg=0x%x, val=0x%x, ret=%d\n",
            mmd, reg, value, ret);
    return MXD_FAIL;
}

/* Helper function to check lane lock status */
static int bit_test(int value, int bit)
{
    return (value >> bit) & 1;
}

/* Read device ID and revision */
static int mxd_read_device_info(struct mxd_priv *priv)
{
    MXD_U16 device_id, revision;
    MXD_STATUS status;

    status = mxd_i2c_read_mdio(priv, 0, MXD_CHIP_REG, MXD_DEVICE_ID_REG, &device_id);
    if (status != MXD_OK) {
        dev_err(&priv->client->dev, "Failed to read device ID\n");
        return -EIO;
    }

    status = mxd_i2c_read_mdio(priv, 0, MXD_CHIP_REG, MXD_DEVICE_REV_REG, &revision);
    if (status != MXD_OK) {
        dev_err(&priv->client->dev, "Failed to read device revision\n");
        return -EIO;
    }

    priv->device_id = device_id;
    priv->revision = revision;

    dev_info(&priv->client->dev, "88x5113 Device ID: 0x%04x, Revision: 0x%04x\n",
             device_id, revision);

    return 0;
}

/* Configure retimer mode for a specific lane */
static int mxd_configure_retimer_lane(struct mxd_priv *priv, int lane, u16 mode)
{
    MXD_U16 reg_addr, reg_val;
    MXD_STATUS status;

    if (lane >= MAX_LANES) {
        return -EINVAL;
    }

    /* Configure lane control register for retimer mode */
    reg_addr = MXD_LANE_CTRL_REG + (lane * 0x100);

    /* Set retimer mode configuration */
    switch (mode) {
    case MXD_RETIMER_25G:
        reg_val = 0x8000;  /* 25G retimer mode */
        break;
    case MXD_RETIMER_10G:
        reg_val = 0x4000;  /* 10G retimer mode */
        break;
    default:
        reg_val = 0x8000;  /* Default to 25G */
        break;
    }

    status = mxd_i2c_write_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, reg_val);
    if (status != MXD_OK) {
        dev_err(&priv->client->dev, "Failed to configure lane %d host side\n", lane);
        return -EIO;
    }

    status = mxd_i2c_write_mdio(priv, 0, MXD_LINE_SIDE, reg_addr, reg_val);
    if (status != MXD_OK) {
        dev_err(&priv->client->dev, "Failed to configure lane %d line side\n", lane);
        return -EIO;
    }

    /* Set VCO code for the lane */
    reg_addr = MXD_VCO_CODE_REG + (lane * 0x100);
    reg_val = priv->vco_codes[lane];

    status = mxd_i2c_write_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, reg_val);
    if (status != MXD_OK) {
        dev_err(&priv->client->dev, "Failed to set VCO code for lane %d\n", lane);
        return -EIO;
    }

    return 0;
}

/* Lane recovery function based on inphi.c pattern */
static int mxd_lane_recovery(int lane)
{
    struct mxd_priv *priv = mxd_private;
    MXD_U16 reg_addr, reg_val;
    MXD_STATUS status;
    int i, retry;

    if (!priv) {
        return -ENODEV;
    }

    if (lane == ALL_LANES) {
        /* Reset all lanes */
        for (i = 0; i < MAX_LANES; i++) {
            /* Reset lane */
            reg_addr = MXD_LANE_CTRL_REG + (i * 0x100);
            status = mxd_i2c_read_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, &reg_val);
            if (status == MXD_OK) {
                reg_val |= 0x0040;  /* Set reset bit */
                mxd_i2c_write_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, reg_val);
                usleep_range(1000, 1200);
                reg_val &= ~0x0040;  /* Clear reset bit */
                mxd_i2c_write_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, reg_val);
            }

            /* Reconfigure retimer mode */
            mxd_configure_retimer_lane(priv, i, MXD_RETIMER_25G);
        }
    } else if (lane >= 0 && lane < MAX_LANES) {
        /* Reset specific lane */
        reg_addr = MXD_LANE_CTRL_REG + (lane * 0x100);
        status = mxd_i2c_read_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, &reg_val);
        if (status == MXD_OK) {
            reg_val |= 0x0040;  /* Set reset bit */
            mxd_i2c_write_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, reg_val);
            usleep_range(1000, 1200);
            reg_val &= ~0x0040;  /* Clear reset bit */
            mxd_i2c_write_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, reg_val);
        }

        /* Reconfigure retimer mode */
        mxd_configure_retimer_lane(priv, lane, MXD_RETIMER_25G);
    }

    /* Wait for lane to stabilize */
    msleep(100);

    return 0;
}

/* Work handler for periodic lane status check */
static void mxd_work_handler(struct work_struct *work)
{
    int all_lanes_lock, lane_lock[MAX_LANES];
    u16 lane_status[MAX_LANES];
    struct mxd_priv *priv = container_of(work, struct mxd_priv, mxd_work.work);
    MXD_U16 reg_addr;
    int i;

    if (!priv || !priv->client) {
        return;
    }

    /* Read lane status registers for all lanes */
    all_lanes_lock = 1;
    for (i = 0; i < MAX_LANES; i++) {
        reg_addr = MXD_LANE_STATUS_REG + (i * 0x100);

        if (mxd_i2c_read_mdio(priv, 0, MXD_HOST_SIDE, reg_addr, &lane_status[i]) == MXD_OK) {
            lane_lock[i] = bit_test(lane_status[i], MXD_LANE_LOCK_BIT);
            priv->lane_status[i] = lane_status[i];

            if (!lane_lock[i]) {
                all_lanes_lock = 0;
            }
        } else {
            lane_lock[i] = 0;
            all_lanes_lock = 0;
        }
    }

    /* Check if any lane has lock from previous stage (e.g., u-boot) */
    if (!all_lanes_lock) {
        int any_lane_lock = 0;
        for (i = 0; i < MAX_LANES; i++) {
            if (lane_lock[i]) {
                any_lane_lock = 1;
                break;
            }
        }

        if (!any_lane_lock) {
            /* Start fresh recovery for all lanes */
            dev_info(&priv->client->dev, "No lanes locked, starting full recovery\n");
            mxd_lane_recovery(ALL_LANES);
        } else {
            /* Recover individual unlocked lanes */
            for (i = 0; i < MAX_LANES; i++) {
                if (!lane_lock[i]) {
                    dev_info(&priv->client->dev, "Lane %d not locked, starting recovery\n", i);
                    mxd_lane_recovery(i);
                }
            }
        }
    }

    /* Schedule next check */
    queue_delayed_work(priv->wq, &priv->mxd_work, priv->onesec);
}

/* Initialize the MXD driver */
static int mxd_init_driver(struct mxd_priv *priv)
{
    int i, ret;

    /* Read device information */
    ret = mxd_read_device_info(priv);
    if (ret) {
        dev_err(&priv->client->dev, "Failed to read device information\n");
        return ret;
    }

    /* Initialize MXD device structure */
    priv->mxd_device.deviceId = priv->device_id;
    priv->mxd_device.chipRevision = priv->revision;
    priv->mxd_device.mdioPort = 0;
    priv->mxd_device.portCount = 1;
    priv->mxd_device.devEnabled = MXD_OK;
    priv->mxd_device.fmxdReadMdio = mxd_i2c_read_mdio;
    priv->mxd_device.fmxdWriteMdio = mxd_i2c_write_mdio;
    priv->mxd_device.hostContext = priv;

    /* Initialize VCO codes based on device characteristics */
    for (i = 0; i < MAX_LANES; i++) {
        priv->vco_codes[i] = 0x0186;  /* Default VCO code for 25G retimer */
        priv->lane_status[i] = 0;
    }

    /* Set retimer mode flag */
    priv->retimer_mode = true;

    /* Configure all lanes for retimer mode */
    for (i = 0; i < MAX_LANES; i++) {
        ret = mxd_configure_retimer_lane(priv, i, MXD_RETIMER_25G);
        if (ret) {
            dev_warn(&priv->client->dev, "Failed to configure lane %d for retimer mode\n", i);
            /* Continue with other lanes */
        }
    }

    /* Perform initial lane recovery to establish links */
    mxd_lane_recovery(ALL_LANES);

    dev_info(&priv->client->dev, "88x5113 initialized in retimer mode\n");

    return 0;
}

/* PHY config_init function */
static int mxd_config_init(struct phy_device *phydev)
{
    struct mxd_priv *priv = mxd_private;

    if (!priv) {
        return -ENODEV;
    }

    /* Set supported features for retimer mode */
    linkmode_zero(phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseCR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseKR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseSR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseCR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseKR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseSR_Full_BIT, phydev->supported);

    linkmode_copy(phydev->advertising, phydev->supported);

    return 0;
}

/* PHY read_status function */
static int mxd_read_status(struct phy_device *phydev)
{
    struct mxd_priv *priv = mxd_private;
    int i, any_link = 0;

    if (!priv) {
        phydev->link = 0;
        return 0;
    }

    /* Check if any lane has link */
    for (i = 0; i < MAX_LANES; i++) {
        if (bit_test(priv->lane_status[i], MXD_LANE_LOCK_BIT)) {
            any_link = 1;
            break;
        }
    }

    phydev->link = any_link;
    phydev->speed = SPEED_25000;  /* Default to 25G for retimer mode */
    phydev->duplex = DUPLEX_FULL;
    phydev->autoneg = AUTONEG_DISABLE;  /* Retimer mode doesn't use autoneg */

    return 0;
}

/* PHY driver probe function */
static int mxd_probe(struct phy_device *phydev)
{
    struct device *dev = &phydev->mdio.dev;
    struct mxd_priv *priv;
    struct i2c_adapter *adapter;
    struct i2c_client *client;
    struct i2c_board_info board_info = {
        I2C_BOARD_INFO("88x5113", MXD_I2C_ADDR),
    };
    struct device_node *np = dev->of_node;
    u32 i2c_bus = 1;  /* Default I2C bus */
    int ret;

    /* Get I2C bus number from device tree if available */
    if (np) {
        of_property_read_u32(np, "marvell,i2c-bus", &i2c_bus);
    }

    /* Allocate private data */
    priv = devm_kzalloc(dev, sizeof(*priv), GFP_KERNEL);
    if (!priv)
        return -ENOMEM;

    /* Get I2C adapter */
    adapter = i2c_get_adapter(i2c_bus);
    if (!adapter) {
        dev_err(dev, "Failed to get I2C adapter %u\n", i2c_bus);
        return -ENODEV;
    }

    /* Create I2C client */
    client = i2c_new_client_device(adapter, &board_info);
    i2c_put_adapter(adapter);
    if (IS_ERR(client)) {
        dev_err(dev, "Failed to create I2C client\n");
        return PTR_ERR(client);
    }

    priv->client = client;
    priv->phydev = phydev;

    /* Store private data in PHY device */
    phydev->priv = priv;

    /* Initialize the driver */
    ret = mxd_init_driver(priv);
    if (ret) {
        dev_err(dev, "Failed to initialize MXD driver\n");
        i2c_unregister_device(client);
        return ret;
    }

    /* Set up work queue for monitoring */
    priv->onesec = msecs_to_jiffies(MXD_POLL_DELAY);
    priv->wq = create_singlethread_workqueue("88x5113_wq");
    if (!priv->wq) {
        dev_err(dev, "Error creating workqueue\n");
        i2c_unregister_device(client);
        return -ENOMEM;
    }

    INIT_DELAYED_WORK(&priv->mxd_work, mxd_work_handler);
    queue_delayed_work(priv->wq, &priv->mxd_work, priv->onesec);

    /* Save private data for global access */
    mxd_private = priv;

    dev_info(dev, "Marvell 88x5113 PHY initialized in retimer mode on I2C bus %u\n", i2c_bus);

    return 0;
}

/* PHY driver remove function */
static void mxd_remove(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;

    if (!priv)
        return;

    /* Clean up work queue */
    cancel_delayed_work_sync(&priv->mxd_work);
    destroy_workqueue(priv->wq);

    /* Unregister I2C client */
    if (priv->client)
        i2c_unregister_device(priv->client);

    /* Clear global reference */
    if (mxd_private == priv)
        mxd_private = NULL;

    phydev->priv = NULL;
}

/* PHY suspend function */
static int mxd_suspend(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;

    if (priv && priv->wq) {
        cancel_delayed_work_sync(&priv->mxd_work);
    }

    return 0;
}

/* PHY resume function */
static int mxd_resume(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;

    if (priv && priv->wq) {
        queue_delayed_work(priv->wq, &priv->mxd_work, priv->onesec);
    }

    return 0;
}

/* PHY driver definition */
static struct phy_driver mxd_driver[] = {
{
    .phy_id         = PHY_ID_88X5113,
    .phy_id_mask    = PHY_ID_MASK,
    .name           = "Marvell 88x5113 Retimer",
    .flags          = PHY_HAS_INTERRUPT,
    .probe          = mxd_probe,
    .remove         = mxd_remove,
    .config_init    = mxd_config_init,
    .read_status    = mxd_read_status,
    .suspend        = mxd_suspend,
    .resume         = mxd_resume,
}
};

module_phy_driver(mxd_driver);

static struct mdio_device_id __maybe_unused mxd_tbl[] = {
    { PHY_ID_88X5113, PHY_ID_MASK },
    { }
};

MODULE_DEVICE_TABLE(mdio, mxd_tbl);
MODULE_DESCRIPTION("Marvell 88x5113 PHY driver for retimer mode with I2C communication");
MODULE_AUTHOR("Linux Kernel Network Developers");
MODULE_LICENSE("GPL");
MODULE_VERSION("1.0");