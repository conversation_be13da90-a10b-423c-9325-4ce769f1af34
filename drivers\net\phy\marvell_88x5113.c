// SPDX-License-Identifier: GPL-2.0+
/*
 * Marvell 88x5113 PHY driver for retimer mode with I2C communication
 *
 * This driver implements the Marvell 88x5113 PHY in retimer mode,
 * using I2C communication instead of traditional MDIO.
 * Based on mxd API, mxdSample.c and inphi.c driver patterns.
 * Designed to replace Inphi PHY driver for DPAA2 integration.
 *
 * Key Features:
 * - I2C communication (address 0x77) instead of MDIO
 * - Retimer mode operation for 25G/10G speeds
 * - Integration with DPAA2 MAC drivers (DPMAC3, DPMAC4, DPMAC5)
 * - Lane monitoring and automatic recovery
 * - Comprehensive debug tracing
 *
 * Copyright (C) 2024 Linux Kernel Network Developers
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/phy.h>
#include <linux/i2c.h>
#include <linux/delay.h>
#include <linux/of.h>
#include <linux/workqueue.h>
#include <linux/firmware.h>
#include <linux/crc32.h>
#include <linux/bitops.h>
#include <linux/ethtool.h>
#include <linux/phylink.h>

/* 88x5113 PHY ID and constants */
#define PHY_ID_88X5113          0x02B09AA0
#define PHY_ID_MASK             0xFFFFFFF0

/* I2C communication constants */
#define MXD_I2C_ADDR            0x77    /* Default I2C address */
#define MXD_I2C_BUS_DEFAULT     1       /* Default I2C bus */

/* MXD API constants based on mxdSample.c */
#define MXD_MAX_LANES           4
#define MXD_ALL_LANES           4
#define MXD_POLL_DELAY          2500    /* 2.5 seconds */

/* Retimer mode definitions */
#define MXD_RETIMER_25G         0x0025
#define MXD_RETIMER_10G         0x0010

/* Lane status bits */
#define MXD_LANE_LOCK_BIT       15
#define MXD_LANE_READY_BIT      14

/* MXD API return codes */
#define MXD_OK                  0
#define MXD_FAIL                -1

/* Register definitions for I2C access */
#define MXD_REG_DEVICE_ID       0x0002
#define MXD_REG_REVISION        0x0003
#define MXD_REG_LANE_STATUS     0x1000
#define MXD_REG_LANE_CONTROL    0x1001
#define MXD_REG_RETIMER_MODE    0x1010

/* MXD device structure (based on mxd API) */
struct mxd_device {
    u16 deviceId;
    u16 chipRevision;
    u16 mdioPort;
    u16 portCount;
    int devEnabled;
    int (*fmxdReadMdio)(void *hostContext, u16 mdioPort, u16 dev, u16 reg, u16 *data);
    int (*fmxdWriteMdio)(void *hostContext, u16 mdioPort, u16 dev, u16 reg, u16 data);
    void *hostContext;
};

/* Private data structure */
struct mxd_priv {
    struct i2c_client *client;
    struct phy_device *phydev;
    struct mxd_device mxd_device;
    struct delayed_work mxd_work;
    struct workqueue_struct *wq;
    unsigned long onesec;

    /* Device information */
    u16 device_id;
    u16 revision;

    /* Lane status and configuration */
    u16 lane_status[MXD_MAX_LANES];
    u16 vco_codes[MXD_MAX_LANES];
    bool retimer_mode;

    /* MAC integration support */
    void (*mac_callback)(void *data, bool link_up);
    void *mac_callback_data;
    char mac_driver_name[32];
};

/* Global reference for MAC integration */
static struct mxd_priv *mxd_global_priv = NULL;

/* Debug macros */
#define mxd_dbg(priv, fmt, args...) \
    dev_info(&(priv)->client->dev, "[88x5113-PHY] " fmt, ##args)

#define mxd_err(priv, fmt, args...) \
    dev_err(&(priv)->client->dev, "[88x5113-PHY] " fmt, ##args)

#define mxd_warn(priv, fmt, args...) \
    dev_warn(&(priv)->client->dev, "[88x5113-PHY] " fmt, ##args)

/* I2C communication functions (replacing MDIO) */
static int mxd_i2c_read_reg(struct mxd_priv *priv, u16 reg, u16 *data)
{
    struct i2c_client *client = priv->client;
    u8 reg_buf[2] = { (reg >> 8) & 0xFF, reg & 0xFF };
    u8 data_buf[2];
    struct i2c_msg msgs[2] = {
        {
            .addr = client->addr,
            .flags = 0,
            .len = 2,
            .buf = reg_buf,
        },
        {
            .addr = client->addr,
            .flags = I2C_M_RD,
            .len = 2,
            .buf = data_buf,
        }
    };
    int ret;

    ret = i2c_transfer(client->adapter, msgs, 2);
    if (ret != 2) {
        mxd_err(priv, "I2C read failed for reg 0x%04x: %d\n", reg, ret);
        return ret < 0 ? ret : -EIO;
    }

    *data = (data_buf[0] << 8) | data_buf[1];
    return 0;
}

static int mxd_i2c_write_reg(struct mxd_priv *priv, u16 reg, u16 data)
{
    struct i2c_client *client = priv->client;
    u8 buf[4] = {
        (reg >> 8) & 0xFF, reg & 0xFF,
        (data >> 8) & 0xFF, data & 0xFF
    };
    struct i2c_msg msg = {
        .addr = client->addr,
        .flags = 0,
        .len = 4,
        .buf = buf,
    };
    int ret;

    ret = i2c_transfer(client->adapter, &msg, 1);
    if (ret != 1) {
        mxd_err(priv, "I2C write failed for reg 0x%04x: %d\n", reg, ret);
        return ret < 0 ? ret : -EIO;
    }

    return 0;
}

/* MXD API wrapper functions for I2C */
static int mxd_i2c_read_mdio(void *hostContext, u16 mdioPort, u16 dev, u16 reg, u16 *data)
{
    struct mxd_priv *priv = (struct mxd_priv *)hostContext;
    u16 full_reg = (dev << 12) | reg;  /* Combine device and register */

    return mxd_i2c_read_reg(priv, full_reg, data);
}

static int mxd_i2c_write_mdio(void *hostContext, u16 mdioPort, u16 dev, u16 reg, u16 data)
{
    struct mxd_priv *priv = (struct mxd_priv *)hostContext;
    u16 full_reg = (dev << 12) | reg;  /* Combine device and register */

    return mxd_i2c_write_reg(priv, full_reg, data);
}

/* Utility functions */
static inline bool bit_test(u16 value, int bit)
{
    return !!(value & BIT(bit));
}

static inline void bit_set(u16 *value, int bit)
{
    *value |= BIT(bit);
}

static inline void bit_clear(u16 *value, int bit)
{
    *value &= ~BIT(bit);
}

/* MXD API functions based on mxdSample.c */
static int mxd_read_device_info(struct mxd_priv *priv)
{
    int ret;

    mxd_dbg(priv, "Reading device information\n");

    ret = mxd_i2c_read_reg(priv, MXD_REG_DEVICE_ID, &priv->device_id);
    if (ret) {
        mxd_err(priv, "Failed to read device ID: %d\n", ret);
        return ret;
    }

    ret = mxd_i2c_read_reg(priv, MXD_REG_REVISION, &priv->revision);
    if (ret) {
        mxd_err(priv, "Failed to read revision: %d\n", ret);
        return ret;
    }

    mxd_dbg(priv, "Device ID: 0x%04x, Revision: 0x%04x\n",
            priv->device_id, priv->revision);

    return 0;
}

static int mxd_configure_retimer_lane(struct mxd_priv *priv, int lane, u16 mode)
{
    u16 reg_addr = MXD_REG_RETIMER_MODE + lane;
    int ret;

    mxd_dbg(priv, "Configuring lane %d for retimer mode 0x%04x\n", lane, mode);

    ret = mxd_i2c_write_reg(priv, reg_addr, mode);
    if (ret) {
        mxd_err(priv, "Failed to configure lane %d: %d\n", lane, ret);
        return ret;
    }

    /* Add delay for configuration to take effect */
    msleep(100);

    return 0;
}

static int mxd_lane_recovery(int lane_mask)
{
    struct mxd_priv *priv = mxd_global_priv;
    int i, ret = 0;

    if (!priv) {
        pr_err("[88x5113-PHY] No global private data for lane recovery\n");
        return -ENODEV;
    }

    mxd_dbg(priv, "Triggering lane recovery for mask 0x%x\n", lane_mask);

    if (lane_mask == MXD_ALL_LANES) {
        /* Recover all lanes */
        for (i = 0; i < MXD_MAX_LANES; i++) {
            ret = mxd_configure_retimer_lane(priv, i, MXD_RETIMER_25G);
            if (ret) {
                mxd_err(priv, "Lane recovery failed for lane %d: %d\n", i, ret);
                break;
            }
        }
    } else {
        /* Recover individual lanes */
        for (i = 0; i < MXD_MAX_LANES; i++) {
            if (lane_mask & BIT(i)) {
                ret = mxd_configure_retimer_lane(priv, i, MXD_RETIMER_25G);
                if (ret) {
                    mxd_err(priv, "Lane recovery failed for lane %d: %d\n", i, ret);
                    break;
                }
            }
        }
    }

    return ret;
}

static int mxd_check_lane_status(struct mxd_priv *priv)
{
    int i, ret;
    u16 status;
    bool any_change = false;

    for (i = 0; i < MXD_MAX_LANES; i++) {
        ret = mxd_i2c_read_reg(priv, MXD_REG_LANE_STATUS + i, &status);
        if (ret) {
            mxd_err(priv, "Failed to read lane %d status: %d\n", i, ret);
            continue;
        }

        if (priv->lane_status[i] != status) {
            mxd_dbg(priv, "Lane %d status changed: 0x%04x -> 0x%04x\n",
                    i, priv->lane_status[i], status);
            priv->lane_status[i] = status;
            any_change = true;
        }
    }

    return any_change ? 1 : 0;
}

/* Work handler for continuous monitoring (based on mxdSample.c pattern) */
static void mxd_work_handler(struct work_struct *work)
{
    struct mxd_priv *priv = container_of(work, struct mxd_priv, mxd_work.work);
    bool all_lanes_lock = true;
    int i, ret;

    /* Check lane status */
    ret = mxd_check_lane_status(priv);
    if (ret < 0) {
        mxd_err(priv, "Failed to check lane status\n");
        goto reschedule;
    }

    /* Check if all lanes are locked */
    for (i = 0; i < MXD_MAX_LANES; i++) {
        if (!bit_test(priv->lane_status[i], MXD_LANE_LOCK_BIT)) {
            all_lanes_lock = false;
            break;
        }
    }

    /* Trigger recovery if needed */
    if (!all_lanes_lock) {
        static int recovery_count = 0;
        if (++recovery_count >= 10) {  /* Every 25 seconds */
            mxd_dbg(priv, "Triggering lane recovery due to link loss\n");
            mxd_lane_recovery(MXD_ALL_LANES);
            recovery_count = 0;
        }
    } else {
        /* Reset recovery counter on successful link */
        static int recovery_count = 0;
        recovery_count = 0;
    }

    /* Notify MAC driver of link state changes */
    if (priv->mac_callback) {
        static bool previous_link = false;
        bool current_link = all_lanes_lock;

        if (current_link != previous_link) {
            mxd_dbg(priv, "Link state change detected: %s -> %s\n",
                    previous_link ? "UP" : "DOWN",
                    current_link ? "UP" : "DOWN");

            mxd_dbg(priv, "Calling MAC callback for %s\n",
                    priv->mac_driver_name);

            priv->mac_callback(priv->mac_callback_data, current_link);
            previous_link = current_link;

            mxd_dbg(priv, "MAC callback completed\n");
        }
    }

reschedule:
    /* Schedule next check */
    queue_delayed_work(priv->wq, &priv->mxd_work, priv->onesec);
}

/* Initialize the MXD driver (based on mxdSample.c) */
static int mxd_init_driver(struct mxd_priv *priv)
{
    int i, ret;

    mxd_dbg(priv, "Initializing MXD driver\n");

    /* Read device information */
    ret = mxd_read_device_info(priv);
    if (ret) {
        mxd_err(priv, "Failed to read device information: %d\n", ret);
        return ret;
    }

    /* Initialize MXD device structure */
    priv->mxd_device.deviceId = priv->device_id;
    priv->mxd_device.chipRevision = priv->revision;
    priv->mxd_device.mdioPort = 0;
    priv->mxd_device.portCount = 1;
    priv->mxd_device.devEnabled = MXD_OK;
    priv->mxd_device.fmxdReadMdio = mxd_i2c_read_mdio;
    priv->mxd_device.fmxdWriteMdio = mxd_i2c_write_mdio;
    priv->mxd_device.hostContext = priv;

    /* Initialize VCO codes based on device characteristics */
    for (i = 0; i < MXD_MAX_LANES; i++) {
        priv->vco_codes[i] = 0x0186;  /* Default VCO code for 25G retimer */
        priv->lane_status[i] = 0;
    }

    /* Set retimer mode flag */
    priv->retimer_mode = true;

    /* Configure all lanes for retimer mode */
    for (i = 0; i < MXD_MAX_LANES; i++) {
        ret = mxd_configure_retimer_lane(priv, i, MXD_RETIMER_25G);
        if (ret) {
            mxd_warn(priv, "Failed to configure lane %d for retimer mode: %d\n", i, ret);
            /* Continue with other lanes */
        }
    }

    /* Perform initial lane recovery to establish links */
    mxd_lane_recovery(MXD_ALL_LANES);

    mxd_dbg(priv, "88x5113 initialized in retimer mode\n");

    return 0;
}

/* PHY config_init function */
static int mxd_config_init(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;

    if (!priv) {
        dev_err(&phydev->mdio.dev, "[88x5113-PHY] No private data in config_init\n");
        return -ENODEV;
    }

    dev_info(&phydev->mdio.dev, "[88x5113-PHY] Configuring PHY capabilities\n");

    /* Set supported features for retimer mode */
    linkmode_zero(phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseCR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseKR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseSR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseCR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseKR_Full_BIT, phydev->supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseSR_Full_BIT, phydev->supported);

    linkmode_copy(phydev->advertising, phydev->supported);

    dev_info(&phydev->mdio.dev, "[88x5113-PHY] PHY capabilities configured\n");

    return 0;
}

/* PHY read_status function */
static int mxd_read_status(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;
    int i, any_link = 0;

    if (!priv) {
        phydev->link = 0;
        return 0;
    }

    /* Check if any lane has link */
    for (i = 0; i < MXD_MAX_LANES; i++) {
        if (bit_test(priv->lane_status[i], MXD_LANE_LOCK_BIT)) {
            any_link = 1;
            break;
        }
    }

    phydev->link = any_link;
    phydev->speed = SPEED_25000;  /* Default to 25G for retimer mode */
    phydev->duplex = DUPLEX_FULL;
    phydev->autoneg = AUTONEG_DISABLE;  /* Retimer mode doesn't use autoneg */

    return 0;
}

/* PHY driver probe function */
static int mxd_probe(struct phy_device *phydev)
{
    struct device *dev = &phydev->mdio.dev;
    struct mxd_priv *priv;
    struct i2c_adapter *adapter;
    struct i2c_client *client;
    struct i2c_board_info board_info = {
        I2C_BOARD_INFO("88x5113", MXD_I2C_ADDR),
    };
    struct device_node *np = dev->of_node;
    u32 i2c_bus = MXD_I2C_BUS_DEFAULT;
    int ret;

    dev_info(dev, "[88x5113-PHY] Probe started for PHY ID: 0x%08x at MDIO address %d\n",
             phydev->phy_id, phydev->mdio.addr);

    /* Get I2C bus number from device tree if available */
    if (np) {
        of_property_read_u32(np, "marvell,i2c-bus", &i2c_bus);
        dev_info(dev, "[88x5113-PHY] Using I2C bus %u from device tree\n", i2c_bus);
    } else {
        dev_info(dev, "[88x5113-PHY] Using default I2C bus %u\n", i2c_bus);
    }

    /* Allocate private data */
    dev_info(dev, "[88x5113-PHY] Allocating private data\n");
    priv = devm_kzalloc(dev, sizeof(*priv), GFP_KERNEL);
    if (!priv) {
        dev_err(dev, "[88x5113-PHY] Failed to allocate private data\n");
        return -ENOMEM;
    }

    /* Get I2C adapter */
    dev_info(dev, "[88x5113-PHY] Getting I2C adapter for bus %u\n", i2c_bus);
    adapter = i2c_get_adapter(i2c_bus);
    if (!adapter) {
        dev_err(dev, "[88x5113-PHY] Failed to get I2C adapter %u\n", i2c_bus);
        return -ENODEV;
    }

    /* Create I2C client */
    dev_info(dev, "[88x5113-PHY] Creating I2C client at address 0x%02x\n", MXD_I2C_ADDR);
    client = i2c_new_client_device(adapter, &board_info);
    i2c_put_adapter(adapter);
    if (IS_ERR(client)) {
        dev_err(dev, "[88x5113-PHY] Failed to create I2C client: %ld\n", PTR_ERR(client));
        return PTR_ERR(client);
    }

    priv->client = client;
    priv->phydev = phydev;

    /* Store private data in PHY device */
    phydev->priv = priv;

    /* Initialize the driver */
    dev_info(dev, "[88x5113-PHY] Initializing MXD driver\n");
    ret = mxd_init_driver(priv);
    if (ret) {
        dev_err(dev, "[88x5113-PHY] Failed to initialize MXD driver: %d\n", ret);
        i2c_unregister_device(client);
        return ret;
    }

    /* Set up work queue for monitoring */
    dev_info(dev, "[88x5113-PHY] Setting up monitoring workqueue\n");
    priv->onesec = msecs_to_jiffies(MXD_POLL_DELAY);
    priv->wq = create_singlethread_workqueue("88x5113_wq");
    if (!priv->wq) {
        dev_err(dev, "[88x5113-PHY] Error creating workqueue\n");
        i2c_unregister_device(client);
        return -ENOMEM;
    }

    INIT_DELAYED_WORK(&priv->mxd_work, mxd_work_handler);
    queue_delayed_work(priv->wq, &priv->mxd_work, priv->onesec);

    /* Save private data for global access */
    mxd_global_priv = priv;

    dev_info(dev, "[88x5113-PHY] Probe completed successfully - PHY ready for MAC integration (I2C bus %u)\n",
             i2c_bus);

    return 0;
}

/* PHY driver remove function */
static void mxd_remove(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;

    if (!priv)
        return;

    dev_info(&phydev->mdio.dev, "[88x5113-PHY] Removing PHY driver\n");

    /* Clean up work queue */
    cancel_delayed_work_sync(&priv->mxd_work);
    destroy_workqueue(priv->wq);

    /* Unregister I2C client */
    if (priv->client)
        i2c_unregister_device(priv->client);

    /* Clear global reference */
    if (mxd_global_priv == priv)
        mxd_global_priv = NULL;

    phydev->priv = NULL;

    dev_info(&phydev->mdio.dev, "[88x5113-PHY] PHY driver removed\n");
}

/* PHY suspend function */
static int mxd_suspend(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;

    dev_info(&phydev->mdio.dev, "[88x5113-PHY] Suspending PHY\n");

    if (priv && priv->wq) {
        cancel_delayed_work_sync(&priv->mxd_work);
    }

    return 0;
}

/* PHY resume function */
static int mxd_resume(struct phy_device *phydev)
{
    struct mxd_priv *priv = phydev->priv;

    dev_info(&phydev->mdio.dev, "[88x5113-PHY] Resuming PHY\n");

    if (priv && priv->wq) {
        queue_delayed_work(priv->wq, &priv->mxd_work, priv->onesec);
    }

    return 0;
}

/* PHY driver definition */
static struct phy_driver mxd_driver[] = {
{
    .phy_id         = PHY_ID_88X5113,
    .phy_id_mask    = PHY_ID_MASK,
    .name           = "Marvell 88x5113 Retimer",
    .flags          = PHY_HAS_INTERRUPT,
    .probe          = mxd_probe,
    .remove         = mxd_remove,
    .config_init    = mxd_config_init,
    .read_status    = mxd_read_status,
    .suspend        = mxd_suspend,
    .resume         = mxd_resume,
}
};

module_phy_driver(mxd_driver);

static struct mdio_device_id __maybe_unused mxd_tbl[] = {
    { PHY_ID_88X5113, PHY_ID_MASK },
    { }
};

MODULE_DEVICE_TABLE(mdio, mxd_tbl);

/* MAC Integration Functions - API for DPAA2 MAC driver */

/**
 * mxd_88x5113_is_supported - Check if PHY is 88x5113
 * @phydev: PHY device
 *
 * Returns true if the PHY is a supported 88x5113 device
 */
bool mxd_88x5113_is_supported(struct phy_device *phydev)
{
    bool supported = (phydev->phy_id & PHY_ID_MASK) == PHY_ID_88X5113;

    if (supported) {
        dev_info(&phydev->mdio.dev, "[88x5113-PHY] PHY support check: SUPPORTED (ID: 0x%08x)\n",
                 phydev->phy_id);
    } else {
        dev_dbg(&phydev->mdio.dev, "[88x5113-PHY] PHY support check: NOT SUPPORTED (ID: 0x%08x)\n",
                phydev->phy_id);
    }

    return supported;
}
EXPORT_SYMBOL_GPL(mxd_88x5113_is_supported);

/**
 * mxd_88x5113_init_for_mac - Initialize 88x5113 for MAC integration
 * @phydev: PHY device
 * @mac_name: Name of the MAC driver for identification
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_init_for_mac(struct phy_device *phydev, const char *mac_name)
{
    struct mxd_priv *priv = mxd_global_priv;

    dev_info(&phydev->mdio.dev, "[88x5113-PHY] MAC integration init called for: %s\n",
             mac_name ? mac_name : "unknown");

    if (!priv) {
        dev_err(&phydev->mdio.dev, "[88x5113-PHY] Private data not available\n");
        return -EINVAL;
    }

    if (!mac_name) {
        dev_err(&phydev->mdio.dev, "[88x5113-PHY] MAC name not provided\n");
        return -EINVAL;
    }

    if (!mxd_88x5113_is_supported(phydev)) {
        dev_err(&phydev->mdio.dev, "[88x5113-PHY] PHY not supported (ID: 0x%08x)\n",
                phydev->phy_id);
        return -ENODEV;
    }

    /* Store MAC driver name for identification */
    strncpy(priv->mac_driver_name, mac_name, sizeof(priv->mac_driver_name) - 1);
    priv->mac_driver_name[sizeof(priv->mac_driver_name) - 1] = '\0';

    dev_info(&phydev->mdio.dev,
             "[88x5113-PHY] Successfully initialized for MAC driver: %s (PHY ID: 0x%08x)\n",
             mac_name, phydev->phy_id);

    /* Print current PHY status */
    dev_info(&phydev->mdio.dev,
             "[88x5113-PHY] Device ID: 0x%04x, Revision: 0x%04x\n",
             priv->device_id, priv->revision);

    return 0;
}
EXPORT_SYMBOL_GPL(mxd_88x5113_init_for_mac);

/**
 * mxd_88x5113_configure_retimer - Configure PHY for retimer mode
 * @phydev: PHY device
 * @speed: Target speed (10000 or 25000 Mbps)
 * @lane_count: Number of lanes to configure
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_configure_retimer(struct phy_device *phydev, u32 speed, u8 lane_count)
{
    struct mxd_priv *priv = mxd_global_priv;
    int i, ret;
    u16 mode;

    dev_info(&phydev->mdio.dev,
             "[88x5113-PHY] Configure retimer called: speed=%u Mbps, lanes=%u\n",
             speed, lane_count);

    if (!priv) {
        dev_err(&phydev->mdio.dev, "[88x5113-PHY] Private data not available for retimer config\n");
        return -ENODEV;
    }

    if (lane_count > MXD_MAX_LANES) {
        dev_err(&phydev->mdio.dev,
                "[88x5113-PHY] Invalid lane count: %u (max: %u)\n",
                lane_count, MXD_MAX_LANES);
        return -EINVAL;
    }

    /* Determine retimer mode based on speed */
    switch (speed) {
    case 25000:
        mode = MXD_RETIMER_25G;
        dev_info(&phydev->mdio.dev, "[88x5113-PHY] Using 25G retimer mode\n");
        break;
    case 10000:
        mode = MXD_RETIMER_10G;
        dev_info(&phydev->mdio.dev, "[88x5113-PHY] Using 10G retimer mode\n");
        break;
    default:
        dev_err(&phydev->mdio.dev,
                "[88x5113-PHY] Unsupported speed: %u Mbps\n", speed);
        return -EINVAL;
    }

    /* Configure each lane for retimer mode */
    for (i = 0; i < lane_count; i++) {
        dev_info(&phydev->mdio.dev,
                 "[88x5113-PHY] Configuring lane %d for retimer mode 0x%04x\n",
                 i, mode);

        ret = mxd_configure_retimer_lane(priv, i, mode);
        if (ret) {
            dev_err(&phydev->mdio.dev,
                    "[88x5113-PHY] Failed to configure lane %d for retimer mode: %d\n",
                    i, ret);
            return ret;
        }

        dev_info(&phydev->mdio.dev, "[88x5113-PHY] Lane %d configured successfully\n", i);
    }

    dev_info(&phydev->mdio.dev,
             "[88x5113-PHY] Retimer configuration completed: %u Mbps, %u lanes\n",
             speed, lane_count);

    return 0;
}
EXPORT_SYMBOL_GPL(mxd_88x5113_configure_retimer);

/**
 * mxd_88x5113_get_lane_status - Get status of all lanes
 * @phydev: PHY device
 * @lane_status: Array to store lane status (must be at least MXD_MAX_LANES elements)
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_get_lane_status(struct phy_device *phydev, u16 *lane_status)
{
    struct mxd_priv *priv = mxd_global_priv;
    int i;

    if (!priv || !lane_status) {
        return -EINVAL;
    }

    /* Copy current lane status */
    for (i = 0; i < MXD_MAX_LANES; i++) {
        lane_status[i] = priv->lane_status[i];
    }

    return 0;
}
EXPORT_SYMBOL_GPL(mxd_88x5113_get_lane_status);

/**
 * mxd_88x5113_is_link_up - Check if retimer link is established
 * @phydev: PHY device
 *
 * Returns true if any lane has link, false otherwise
 */
bool mxd_88x5113_is_link_up(struct phy_device *phydev)
{
    struct mxd_priv *priv = mxd_global_priv;
    int i;

    if (!priv) {
        return false;
    }

    /* Check if any lane has link */
    for (i = 0; i < MXD_MAX_LANES; i++) {
        if (bit_test(priv->lane_status[i], MXD_LANE_LOCK_BIT)) {
            return true;
        }
    }

    return false;
}
EXPORT_SYMBOL_GPL(mxd_88x5113_is_link_up);

/**
 * mxd_88x5113_get_speed - Get current operating speed
 * @phydev: PHY device
 *
 * Returns current speed in Mbps, 0 if no link
 */
u32 mxd_88x5113_get_speed(struct phy_device *phydev)
{
    if (mxd_88x5113_is_link_up(phydev)) {
        /* Return default speed for retimer mode */
        return 25000;  /* 25G default */
    }

    return 0;
}
EXPORT_SYMBOL_GPL(mxd_88x5113_get_speed);

/**
 * mxd_88x5113_register_mac_callback - Register MAC driver callback
 * @phydev: PHY device
 * @callback: Callback function to call on link state changes
 * @data: Private data to pass to callback
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_register_mac_callback(struct phy_device *phydev,
                                      void (*callback)(void *data, bool link_up),
                                      void *data)
{
    struct mxd_priv *priv = mxd_global_priv;

    dev_info(&phydev->mdio.dev,
             "[88x5113-PHY] MAC callback registration requested\n");

    if (!priv) {
        dev_err(&phydev->mdio.dev, "[88x5113-PHY] Private data not available for callback\n");
        return -EINVAL;
    }

    if (!callback) {
        dev_err(&phydev->mdio.dev, "[88x5113-PHY] Callback function is NULL\n");
        return -EINVAL;
    }

    if (!data) {
        dev_warn(&phydev->mdio.dev, "[88x5113-PHY] Callback data is NULL\n");
    }

    priv->mac_callback = callback;
    priv->mac_callback_data = data;

    dev_info(&phydev->mdio.dev,
             "[88x5113-PHY] MAC callback registered successfully (MAC: %s)\n",
             priv->mac_driver_name);

    return 0;
}
EXPORT_SYMBOL_GPL(mxd_88x5113_register_mac_callback);

/**
 * mxd_88x5113_unregister_mac_callback - Unregister MAC driver callback
 * @phydev: PHY device
 */
void mxd_88x5113_unregister_mac_callback(struct phy_device *phydev)
{
    struct mxd_priv *priv = mxd_global_priv;

    if (priv) {
        priv->mac_callback = NULL;
        priv->mac_callback_data = NULL;
        dev_info(&phydev->mdio.dev, "[88x5113-PHY] MAC callback unregistered\n");
    }
}
EXPORT_SYMBOL_GPL(mxd_88x5113_unregister_mac_callback);

MODULE_DESCRIPTION("Marvell 88x5113 PHY driver for retimer mode with I2C communication");
MODULE_AUTHOR("Linux Kernel Network Developers");
MODULE_LICENSE("GPL");
MODULE_VERSION("1.0");