/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Marvell 88x5113 PHY driver header for MAC integration
 *
 * This header provides the API for MAC drivers to integrate with
 * the Marvell 88x5113 PHY in retimer mode.
 *
 * Copyright (C) 2024 Linux Kernel Network Developers
 */

#ifndef _MARVELL_88X5113_H_
#define _MARVELL_88X5113_H_

#include <linux/phy.h>

/* 88x5113 PHY constants */
#define MXD_88X5113_MAX_LANES       4
#define MXD_88X5113_LANE_MASK_ALL   0xF

/* Lane mask definitions for recovery operations */
#define MXD_88X5113_LANE_0          BIT(0)
#define MXD_88X5113_LANE_1          BIT(1)
#define MXD_88X5113_LANE_2          BIT(2)
#define MXD_88X5113_LANE_3          BIT(3)

#ifdef CONFIG_MARVELL_88X5113_PHY

/**
 * mxd_88x5113_is_supported - Check if PHY is 88x5113
 * @phydev: PHY device
 *
 * Returns true if the PHY is a supported 88x5113 device
 */
bool mxd_88x5113_is_supported(struct phy_device *phydev);

/**
 * mxd_88x5113_init_for_mac - Initialize 88x5113 for MAC integration
 * @phydev: PHY device
 * @mac_name: Name of the MAC driver for identification
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_init_for_mac(struct phy_device *phydev, const char *mac_name);

/**
 * mxd_88x5113_configure_retimer - Configure PHY for retimer mode
 * @phydev: PHY device
 * @speed: Target speed (10000 or 25000 Mbps)
 * @lane_count: Number of lanes to configure
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_configure_retimer(struct phy_device *phydev, u32 speed, u8 lane_count);

/**
 * mxd_88x5113_get_lane_status - Get status of all lanes
 * @phydev: PHY device
 * @lane_status: Array to store lane status (must be at least MXD_88X5113_MAX_LANES elements)
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_get_lane_status(struct phy_device *phydev, u16 *lane_status);

/**
 * mxd_88x5113_is_link_up - Check if retimer link is established
 * @phydev: PHY device
 *
 * Returns true if any lane has link, false otherwise
 */
bool mxd_88x5113_is_link_up(struct phy_device *phydev);

/**
 * mxd_88x5113_get_speed - Get current operating speed
 * @phydev: PHY device
 *
 * Returns current speed in Mbps, 0 if no link
 */
u32 mxd_88x5113_get_speed(struct phy_device *phydev);

/**
 * mxd_88x5113_register_mac_callback - Register MAC driver callback
 * @phydev: PHY device
 * @callback: Callback function to call on link state changes
 * @data: Private data to pass to callback
 *
 * Returns 0 on success, negative error code on failure
 */
int mxd_88x5113_register_mac_callback(struct phy_device *phydev,
                                      void (*callback)(void *data, bool link_up),
                                      void *data);

/**
 * mxd_88x5113_unregister_mac_callback - Unregister MAC driver callback
 * @phydev: PHY device
 */
void mxd_88x5113_unregister_mac_callback(struct phy_device *phydev);

#else /* !CONFIG_MARVELL_88X5113_PHY */

/* Stub functions when 88x5113 PHY driver is not enabled */

static inline bool mxd_88x5113_is_supported(struct phy_device *phydev)
{
    return false;
}

static inline int mxd_88x5113_init_for_mac(struct phy_device *phydev, const char *mac_name)
{
    return -ENODEV;
}

static inline int mxd_88x5113_configure_retimer(struct phy_device *phydev, u32 speed, u8 lane_count)
{
    return -ENODEV;
}

static inline int mxd_88x5113_get_lane_status(struct phy_device *phydev, u16 *lane_status)
{
    return -ENODEV;
}

static inline bool mxd_88x5113_is_link_up(struct phy_device *phydev)
{
    return false;
}

static inline u32 mxd_88x5113_get_speed(struct phy_device *phydev)
{
    return 0;
}

static inline int mxd_88x5113_register_mac_callback(struct phy_device *phydev,
                                                    void (*callback)(void *data, bool link_up),
                                                    void *data)
{
    return -ENODEV;
}

static inline void mxd_88x5113_unregister_mac_callback(struct phy_device *phydev)
{
}

#endif /* CONFIG_MARVELL_88X5113_PHY */

/**
 * struct mxd_88x5113_ops - Operations structure for 88x5113 PHY
 *
 * This structure provides a consistent interface for MAC drivers
 * to interact with the 88x5113 PHY driver.
 */
struct mxd_88x5113_ops {
    bool (*is_supported)(struct phy_device *phydev);
    int (*init_for_mac)(struct phy_device *phydev, const char *mac_name);
    int (*configure_retimer)(struct phy_device *phydev, u32 speed, u8 lane_count);
    int (*get_lane_status)(struct phy_device *phydev, u16 *lane_status);
    bool (*is_link_up)(struct phy_device *phydev);
    u32 (*get_speed)(struct phy_device *phydev);
    int (*register_mac_callback)(struct phy_device *phydev,
                                 void (*callback)(void *data, bool link_up),
                                 void *data);
    void (*unregister_mac_callback)(struct phy_device *phydev);
};

/**
 * mxd_88x5113_get_ops - Get operations structure for 88x5113 PHY
 * @phydev: PHY device
 *
 * Returns pointer to operations structure if PHY is supported, NULL otherwise
 */
static inline const struct mxd_88x5113_ops *mxd_88x5113_get_ops(struct phy_device *phydev)
{
    static const struct mxd_88x5113_ops ops = {
        .is_supported = mxd_88x5113_is_supported,
        .init_for_mac = mxd_88x5113_init_for_mac,
        .configure_retimer = mxd_88x5113_configure_retimer,
        .get_lane_status = mxd_88x5113_get_lane_status,
        .is_link_up = mxd_88x5113_is_link_up,
        .get_speed = mxd_88x5113_get_speed,
        .register_mac_callback = mxd_88x5113_register_mac_callback,
        .unregister_mac_callback = mxd_88x5113_unregister_mac_callback,
    };

    if (mxd_88x5113_is_supported(phydev))
        return &ops;

    return NULL;
}

#endif /* _MARVELL_88X5113_H_ */
