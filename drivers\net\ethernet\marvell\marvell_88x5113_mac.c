// SPDX-License-Identifier: GPL-2.0+
/*
 * Marvell 88x5113 MAC driver
 *
 * This driver provides MAC functionality for systems using the
 * Marvell 88x5113 PHY in retimer mode. It integrates with the
 * 88x5113 PHY driver and provides standard Ethernet MAC operations.
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/platform_device.h>
#include <linux/etherdevice.h>
#include <linux/netdevice.h>
#include <linux/phy.h>
#include <linux/phylink.h>
#include <linux/of.h>
#include <linux/of_net.h>
#include <linux/of_mdio.h>
#include <linux/interrupt.h>
#include <linux/dma-mapping.h>
#include <linux/clk.h>
#include <linux/reset.h>
#include <linux/io.h>
#include <linux/delay.h>

#define DRIVER_NAME "marvell-88x5113-mac"
#define DRIVER_VERSION "1.0"

/* MAC register definitions */
#define MAC_CTRL_REG            0x0000
#define MAC_STATUS_REG          0x0004
#define MAC_ADDR_LOW_REG        0x0008
#define MAC_ADDR_HIGH_REG       0x000C
#define MAC_TX_CTRL_REG         0x0010
#define MAC_RX_CTRL_REG         0x0014
#define MAC_INT_STATUS_REG      0x0018
#define MAC_INT_ENABLE_REG      0x001C

/* MAC control register bits */
#define MAC_CTRL_ENABLE         BIT(0)
#define MAC_CTRL_RESET          BIT(1)
#define MAC_CTRL_LOOPBACK       BIT(2)
#define MAC_CTRL_SPEED_MASK     GENMASK(5, 4)
#define MAC_CTRL_SPEED_10G      (0x2 << 4)
#define MAC_CTRL_SPEED_25G      (0x3 << 4)
#define MAC_CTRL_DUPLEX_FULL    BIT(6)

/* MAC status register bits */
#define MAC_STATUS_LINK_UP      BIT(0)
#define MAC_STATUS_TX_READY     BIT(1)
#define MAC_STATUS_RX_READY     BIT(2)

/* TX/RX control register bits */
#define MAC_TX_ENABLE           BIT(0)
#define MAC_RX_ENABLE           BIT(0)

/* Interrupt bits */
#define MAC_INT_LINK_CHANGE     BIT(0)
#define MAC_INT_TX_COMPLETE     BIT(1)
#define MAC_INT_RX_COMPLETE     BIT(2)
#define MAC_INT_ERROR           BIT(3)

/* DMA descriptor definitions */
#define MAC_DMA_DESC_SIZE       16
#define MAC_TX_RING_SIZE        256
#define MAC_RX_RING_SIZE        256

/* Buffer sizes */
#define MAC_MAX_FRAME_SIZE      9600
#define MAC_RX_BUF_SIZE         2048

struct mac_dma_desc {
    u32 status;
    u32 length;
    dma_addr_t buffer;
    u32 reserved;
};

struct marvell_mac_priv {
    struct net_device *netdev;
    struct device *dev;
    void __iomem *base;

    /* PHY and phylink */
    struct phylink *phylink;
    struct phylink_config phylink_config;
    struct phy_device *phydev;
    phy_interface_t phy_interface;

    /* Clocks and resets */
    struct clk *mac_clk;
    struct clk *phy_clk;
    struct reset_control *mac_rst;

    /* DMA and buffers */
    struct mac_dma_desc *tx_ring;
    struct mac_dma_desc *rx_ring;
    dma_addr_t tx_ring_dma;
    dma_addr_t rx_ring_dma;
    struct sk_buff **tx_skbs;
    struct sk_buff **rx_skbs;

    /* Ring management */
    unsigned int tx_head;
    unsigned int tx_tail;
    unsigned int rx_head;
    unsigned int rx_tail;

    /* Statistics */
    struct net_device_stats stats;

    /* Locks */
    spinlock_t tx_lock;
    spinlock_t rx_lock;

    /* NAPI */
    struct napi_struct napi;

    /* Work queue for link monitoring */
    struct workqueue_struct *wq;
    struct delayed_work link_work;

    /* Configuration */
    bool retimer_mode;
    u32 max_speed;
    int irq;
};

/* Forward declarations */
static void marvell_mac_phylink_validate(struct phylink_config *config,
                                         unsigned long *supported,
                                         struct phylink_link_state *state);
static void marvell_mac_phylink_mac_config(struct phylink_config *config,
                                           unsigned int mode,
                                           const struct phylink_link_state *state);
static void marvell_mac_phylink_mac_link_down(struct phylink_config *config,
                                              unsigned int mode,
                                              phy_interface_t interface);
static void marvell_mac_phylink_mac_link_up(struct phylink_config *config,
                                            struct phy_device *phy,
                                            unsigned int mode,
                                            phy_interface_t interface,
                                            int speed, int duplex,
                                            bool tx_pause, bool rx_pause);

static const struct phylink_mac_ops marvell_mac_phylink_ops = {
    .validate = marvell_mac_phylink_validate,
    .mac_config = marvell_mac_phylink_mac_config,
    .mac_link_down = marvell_mac_phylink_mac_link_down,
    .mac_link_up = marvell_mac_phylink_mac_link_up,
};

/* MAC register access helpers */
static inline u32 marvell_mac_readl(struct marvell_mac_priv *priv, u32 reg)
{
    return readl(priv->base + reg);
}

static inline void marvell_mac_writel(struct marvell_mac_priv *priv, u32 reg, u32 val)
{
    writel(val, priv->base + reg);
}

/* MAC hardware initialization */
static int marvell_mac_hw_init(struct marvell_mac_priv *priv)
{
    u32 ctrl;
    int ret;

    /* Enable clocks */
    ret = clk_prepare_enable(priv->mac_clk);
    if (ret) {
        dev_err(priv->dev, "Failed to enable MAC clock: %d\n", ret);
        return ret;
    }

    if (priv->phy_clk) {
        ret = clk_prepare_enable(priv->phy_clk);
        if (ret) {
            dev_err(priv->dev, "Failed to enable PHY clock: %d\n", ret);
            clk_disable_unprepare(priv->mac_clk);
            return ret;
        }
    }

    /* Reset MAC */
    if (priv->mac_rst) {
        reset_control_assert(priv->mac_rst);
        usleep_range(10, 20);
        reset_control_deassert(priv->mac_rst);
        usleep_range(10, 20);
    }

    /* Software reset */
    marvell_mac_writel(priv, MAC_CTRL_REG, MAC_CTRL_RESET);
    usleep_range(100, 200);

    /* Configure MAC for retimer mode */
    ctrl = MAC_CTRL_ENABLE | MAC_CTRL_DUPLEX_FULL;
    if (priv->max_speed >= 25000)
        ctrl |= MAC_CTRL_SPEED_25G;
    else
        ctrl |= MAC_CTRL_SPEED_10G;

    marvell_mac_writel(priv, MAC_CTRL_REG, ctrl);

    /* Enable TX and RX */
    marvell_mac_writel(priv, MAC_TX_CTRL_REG, MAC_TX_ENABLE);
    marvell_mac_writel(priv, MAC_RX_CTRL_REG, MAC_RX_ENABLE);

    /* Enable interrupts */
    marvell_mac_writel(priv, MAC_INT_ENABLE_REG,
                       MAC_INT_LINK_CHANGE | MAC_INT_TX_COMPLETE |
                       MAC_INT_RX_COMPLETE | MAC_INT_ERROR);

    dev_info(priv->dev, "MAC hardware initialized for %s mode\n",
             priv->retimer_mode ? "retimer" : "normal");

    return 0;
}

/* MAC hardware cleanup */
static void marvell_mac_hw_cleanup(struct marvell_mac_priv *priv)
{
    /* Disable interrupts */
    marvell_mac_writel(priv, MAC_INT_ENABLE_REG, 0);

    /* Disable TX and RX */
    marvell_mac_writel(priv, MAC_TX_CTRL_REG, 0);
    marvell_mac_writel(priv, MAC_RX_CTRL_REG, 0);

    /* Disable MAC */
    marvell_mac_writel(priv, MAC_CTRL_REG, 0);

    /* Disable clocks */
    if (priv->phy_clk)
        clk_disable_unprepare(priv->phy_clk);
    clk_disable_unprepare(priv->mac_clk);
}

/* Set MAC address */
static int marvell_mac_set_mac_address(struct net_device *netdev, void *addr)
{
    struct marvell_mac_priv *priv = netdev_priv(netdev);
    struct sockaddr *sa = addr;
    u32 addr_low, addr_high;

    if (!is_valid_ether_addr(sa->sa_data))
        return -EADDRNOTAVAIL;

    memcpy(netdev->dev_addr, sa->sa_data, ETH_ALEN);

    /* Program MAC address to hardware */
    addr_low = (netdev->dev_addr[3] << 24) | (netdev->dev_addr[2] << 16) |
               (netdev->dev_addr[1] << 8) | netdev->dev_addr[0];
    addr_high = (netdev->dev_addr[5] << 8) | netdev->dev_addr[4];

    marvell_mac_writel(priv, MAC_ADDR_LOW_REG, addr_low);
    marvell_mac_writel(priv, MAC_ADDR_HIGH_REG, addr_high);

    return 0;
}

/* DMA ring management */
static int marvell_mac_alloc_rings(struct marvell_mac_priv *priv)
{
    struct device *dev = priv->dev;
    size_t tx_ring_size, rx_ring_size;
    int i;

    /* Allocate TX ring */
    tx_ring_size = MAC_TX_RING_SIZE * sizeof(struct mac_dma_desc);
    priv->tx_ring = dma_alloc_coherent(dev, tx_ring_size,
                                       &priv->tx_ring_dma, GFP_KERNEL);
    if (!priv->tx_ring)
        return -ENOMEM;

    /* Allocate RX ring */
    rx_ring_size = MAC_RX_RING_SIZE * sizeof(struct mac_dma_desc);
    priv->rx_ring = dma_alloc_coherent(dev, rx_ring_size,
                                       &priv->rx_ring_dma, GFP_KERNEL);
    if (!priv->rx_ring)
        goto err_free_tx_ring;

    /* Allocate SKB arrays */
    priv->tx_skbs = kcalloc(MAC_TX_RING_SIZE, sizeof(struct sk_buff *), GFP_KERNEL);
    if (!priv->tx_skbs)
        goto err_free_rx_ring;

    priv->rx_skbs = kcalloc(MAC_RX_RING_SIZE, sizeof(struct sk_buff *), GFP_KERNEL);
    if (!priv->rx_skbs)
        goto err_free_tx_skbs;

    /* Initialize RX buffers */
    for (i = 0; i < MAC_RX_RING_SIZE; i++) {
        struct sk_buff *skb;
        dma_addr_t dma_addr;

        skb = netdev_alloc_skb(priv->netdev, MAC_RX_BUF_SIZE);
        if (!skb)
            goto err_free_rx_skbs;

        dma_addr = dma_map_single(dev, skb->data, MAC_RX_BUF_SIZE, DMA_FROM_DEVICE);
        if (dma_mapping_error(dev, dma_addr)) {
            dev_kfree_skb(skb);
            goto err_free_rx_skbs;
        }

        priv->rx_skbs[i] = skb;
        priv->rx_ring[i].buffer = dma_addr;
        priv->rx_ring[i].length = MAC_RX_BUF_SIZE;
        priv->rx_ring[i].status = 0;
    }

    /* Initialize ring pointers */
    priv->tx_head = 0;
    priv->tx_tail = 0;
    priv->rx_head = 0;
    priv->rx_tail = 0;

    return 0;

err_free_rx_skbs:
    for (i = 0; i < MAC_RX_RING_SIZE; i++) {
        if (priv->rx_skbs[i]) {
            dma_unmap_single(dev, priv->rx_ring[i].buffer,
                           MAC_RX_BUF_SIZE, DMA_FROM_DEVICE);
            dev_kfree_skb(priv->rx_skbs[i]);
        }
    }
    kfree(priv->rx_skbs);
err_free_tx_skbs:
    kfree(priv->tx_skbs);
err_free_rx_ring:
    dma_free_coherent(dev, rx_ring_size, priv->rx_ring, priv->rx_ring_dma);
err_free_tx_ring:
    dma_free_coherent(dev, tx_ring_size, priv->tx_ring, priv->tx_ring_dma);
    return -ENOMEM;
}

static void marvell_mac_free_rings(struct marvell_mac_priv *priv)
{
    struct device *dev = priv->dev;
    size_t tx_ring_size, rx_ring_size;
    int i;

    /* Free RX buffers */
    for (i = 0; i < MAC_RX_RING_SIZE; i++) {
        if (priv->rx_skbs[i]) {
            dma_unmap_single(dev, priv->rx_ring[i].buffer,
                           MAC_RX_BUF_SIZE, DMA_FROM_DEVICE);
            dev_kfree_skb(priv->rx_skbs[i]);
        }
    }

    /* Free TX buffers */
    for (i = 0; i < MAC_TX_RING_SIZE; i++) {
        if (priv->tx_skbs[i]) {
            dma_unmap_single(dev, priv->tx_ring[i].buffer,
                           priv->tx_skbs[i]->len, DMA_TO_DEVICE);
            dev_kfree_skb(priv->tx_skbs[i]);
        }
    }

    /* Free SKB arrays */
    kfree(priv->rx_skbs);
    kfree(priv->tx_skbs);

    /* Free DMA rings */
    tx_ring_size = MAC_TX_RING_SIZE * sizeof(struct mac_dma_desc);
    rx_ring_size = MAC_RX_RING_SIZE * sizeof(struct mac_dma_desc);

    dma_free_coherent(dev, tx_ring_size, priv->tx_ring, priv->tx_ring_dma);
    dma_free_coherent(dev, rx_ring_size, priv->rx_ring, priv->rx_ring_dma);
}

/* Network device operations */
static int marvell_mac_open(struct net_device *netdev)
{
    struct marvell_mac_priv *priv = netdev_priv(netdev);
    int ret;

    /* Initialize hardware */
    ret = marvell_mac_hw_init(priv);
    if (ret)
        return ret;

    /* Allocate DMA rings */
    ret = marvell_mac_alloc_rings(priv);
    if (ret)
        goto err_hw_cleanup;

    /* Request IRQ */
    ret = request_irq(priv->irq, marvell_mac_interrupt, IRQF_SHARED,
                      netdev->name, netdev);
    if (ret)
        goto err_free_rings;

    /* Enable NAPI */
    napi_enable(&priv->napi);

    /* Start phylink */
    phylink_start(priv->phylink);

    /* Start link monitoring work */
    queue_delayed_work(priv->wq, &priv->link_work, HZ);

    netif_start_queue(netdev);

    dev_info(priv->dev, "Network interface opened\n");

    return 0;

err_free_rings:
    marvell_mac_free_rings(priv);
err_hw_cleanup:
    marvell_mac_hw_cleanup(priv);
    return ret;
}

static int marvell_mac_close(struct net_device *netdev)
{
    struct marvell_mac_priv *priv = netdev_priv(netdev);

    netif_stop_queue(netdev);

    /* Cancel link monitoring work */
    cancel_delayed_work_sync(&priv->link_work);

    /* Stop phylink */
    phylink_stop(priv->phylink);

    /* Disable NAPI */
    napi_disable(&priv->napi);

    /* Free IRQ */
    free_irq(priv->irq, netdev);

    /* Free DMA rings */
    marvell_mac_free_rings(priv);

    /* Cleanup hardware */
    marvell_mac_hw_cleanup(priv);

    dev_info(priv->dev, "Network interface closed\n");

    return 0;
}

/* Transmit function */
static netdev_tx_t marvell_mac_start_xmit(struct sk_buff *skb,
                                          struct net_device *netdev)
{
    struct marvell_mac_priv *priv = netdev_priv(netdev);
    struct mac_dma_desc *desc;
    dma_addr_t dma_addr;
    unsigned long flags;
    unsigned int next_head;

    /* Check if ring is full */
    next_head = (priv->tx_head + 1) % MAC_TX_RING_SIZE;
    if (next_head == priv->tx_tail) {
        netif_stop_queue(netdev);
        return NETDEV_TX_BUSY;
    }

    /* Map SKB for DMA */
    dma_addr = dma_map_single(priv->dev, skb->data, skb->len, DMA_TO_DEVICE);
    if (dma_mapping_error(priv->dev, dma_addr)) {
        dev_kfree_skb(skb);
        priv->stats.tx_dropped++;
        return NETDEV_TX_OK;
    }

    spin_lock_irqsave(&priv->tx_lock, flags);

    /* Setup descriptor */
    desc = &priv->tx_ring[priv->tx_head];
    desc->buffer = dma_addr;
    desc->length = skb->len;
    desc->status = 0x80000000; /* OWN bit */

    /* Store SKB */
    priv->tx_skbs[priv->tx_head] = skb;

    /* Update head pointer */
    priv->tx_head = next_head;

    /* Update statistics */
    priv->stats.tx_packets++;
    priv->stats.tx_bytes += skb->len;

    spin_unlock_irqrestore(&priv->tx_lock, flags);

    /* Trigger transmission */
    marvell_mac_writel(priv, MAC_TX_CTRL_REG,
                       marvell_mac_readl(priv, MAC_TX_CTRL_REG) | BIT(1));

    return NETDEV_TX_OK;
}

/* Get network device statistics */
static struct net_device_stats *marvell_mac_get_stats(struct net_device *netdev)
{
    struct marvell_mac_priv *priv = netdev_priv(netdev);
    return &priv->stats;
}

/* Interrupt handler */
static irqreturn_t marvell_mac_interrupt(int irq, void *dev_id)
{
    struct net_device *netdev = dev_id;
    struct marvell_mac_priv *priv = netdev_priv(netdev);
    u32 int_status;
    irqreturn_t ret = IRQ_NONE;

    /* Read interrupt status */
    int_status = marvell_mac_readl(priv, MAC_INT_STATUS_REG);
    if (!int_status)
        return IRQ_NONE;

    /* Clear interrupts */
    marvell_mac_writel(priv, MAC_INT_STATUS_REG, int_status);

    /* Handle link change */
    if (int_status & MAC_INT_LINK_CHANGE) {
        phylink_mac_change(priv->phylink, true);
        ret = IRQ_HANDLED;
    }

    /* Handle TX/RX completion */
    if (int_status & (MAC_INT_TX_COMPLETE | MAC_INT_RX_COMPLETE)) {
        if (napi_schedule_prep(&priv->napi)) {
            /* Disable TX/RX interrupts */
            marvell_mac_writel(priv, MAC_INT_ENABLE_REG,
                               marvell_mac_readl(priv, MAC_INT_ENABLE_REG) &
                               ~(MAC_INT_TX_COMPLETE | MAC_INT_RX_COMPLETE));
            __napi_schedule(&priv->napi);
        }
        ret = IRQ_HANDLED;
    }

    /* Handle errors */
    if (int_status & MAC_INT_ERROR) {
        dev_err(priv->dev, "MAC error interrupt: 0x%08x\n", int_status);
        priv->stats.rx_errors++;
        ret = IRQ_HANDLED;
    }

    return ret;
}

/* NAPI poll function */
static int marvell_mac_poll(struct napi_struct *napi, int budget)
{
    struct marvell_mac_priv *priv = container_of(napi, struct marvell_mac_priv, napi);
    int work_done = 0;

    /* Process TX completions */
    marvell_mac_tx_complete(priv);

    /* Process RX packets */
    work_done = marvell_mac_rx_poll(priv, budget);

    /* If we processed less than budget, re-enable interrupts */
    if (work_done < budget) {
        napi_complete(napi);
        marvell_mac_writel(priv, MAC_INT_ENABLE_REG,
                           marvell_mac_readl(priv, MAC_INT_ENABLE_REG) |
                           MAC_INT_TX_COMPLETE | MAC_INT_RX_COMPLETE);
    }

    return work_done;
}

/* TX completion processing */
static void marvell_mac_tx_complete(struct marvell_mac_priv *priv)
{
    unsigned long flags;

    spin_lock_irqsave(&priv->tx_lock, flags);

    while (priv->tx_tail != priv->tx_head) {
        struct mac_dma_desc *desc = &priv->tx_ring[priv->tx_tail];
        struct sk_buff *skb = priv->tx_skbs[priv->tx_tail];

        /* Check if descriptor is still owned by hardware */
        if (desc->status & 0x80000000)
            break;

        /* Unmap and free SKB */
        if (skb) {
            dma_unmap_single(priv->dev, desc->buffer, skb->len, DMA_TO_DEVICE);
            dev_kfree_skb_irq(skb);
            priv->tx_skbs[priv->tx_tail] = NULL;
        }

        /* Move to next descriptor */
        priv->tx_tail = (priv->tx_tail + 1) % MAC_TX_RING_SIZE;

        /* Wake up queue if it was stopped */
        if (netif_queue_stopped(priv->netdev))
            netif_wake_queue(priv->netdev);
    }

    spin_unlock_irqrestore(&priv->tx_lock, flags);
}

/* RX polling function */
static int marvell_mac_rx_poll(struct marvell_mac_priv *priv, int budget)
{
    int work_done = 0;
    unsigned long flags;

    spin_lock_irqsave(&priv->rx_lock, flags);

    while (work_done < budget) {
        struct mac_dma_desc *desc = &priv->rx_ring[priv->rx_head];
        struct sk_buff *skb = priv->rx_skbs[priv->rx_head];
        struct sk_buff *new_skb;
        dma_addr_t new_dma_addr;
        u32 status = desc->status;
        u32 length;

        /* Check if descriptor is still owned by hardware */
        if (status & 0x80000000)
            break;

        /* Get packet length */
        length = desc->length & 0xFFFF;

        /* Allocate new SKB for this slot */
        new_skb = netdev_alloc_skb(priv->netdev, MAC_RX_BUF_SIZE);
        if (!new_skb) {
            priv->stats.rx_dropped++;
            break;
        }

        /* Map new SKB */
        new_dma_addr = dma_map_single(priv->dev, new_skb->data,
                                      MAC_RX_BUF_SIZE, DMA_FROM_DEVICE);
        if (dma_mapping_error(priv->dev, new_dma_addr)) {
            dev_kfree_skb(new_skb);
            priv->stats.rx_dropped++;
            break;
        }

        /* Unmap old SKB */
        dma_unmap_single(priv->dev, desc->buffer, MAC_RX_BUF_SIZE, DMA_FROM_DEVICE);

        /* Setup received SKB */
        skb_put(skb, length);
        skb->protocol = eth_type_trans(skb, priv->netdev);

        /* Update statistics */
        priv->stats.rx_packets++;
        priv->stats.rx_bytes += length;

        /* Setup new descriptor */
        priv->rx_skbs[priv->rx_head] = new_skb;
        desc->buffer = new_dma_addr;
        desc->length = MAC_RX_BUF_SIZE;
        desc->status = 0x80000000; /* Give ownership back to hardware */

        /* Move to next descriptor */
        priv->rx_head = (priv->rx_head + 1) % MAC_RX_RING_SIZE;

        spin_unlock_irqrestore(&priv->rx_lock, flags);

        /* Pass SKB to network stack */
        netif_receive_skb(skb);
        work_done++;

        spin_lock_irqsave(&priv->rx_lock, flags);
    }

    spin_unlock_irqrestore(&priv->rx_lock, flags);

    return work_done;
}

/* Link monitoring work function */
static void marvell_mac_link_work(struct work_struct *work)
{
    struct marvell_mac_priv *priv = container_of(work, struct marvell_mac_priv,
                                                  link_work.work);
    u32 status;
    bool link_up;

    /* Read MAC status */
    status = marvell_mac_readl(priv, MAC_STATUS_REG);
    link_up = !!(status & MAC_STATUS_LINK_UP);

    /* Update phylink with current status */
    phylink_mac_change(priv->phylink, link_up);

    /* Schedule next check */
    queue_delayed_work(priv->wq, &priv->link_work, HZ * 2);
}

/* Phylink operations */
static void marvell_mac_phylink_validate(struct phylink_config *config,
                                         unsigned long *supported,
                                         struct phylink_link_state *state)
{
    struct marvell_mac_priv *priv = container_of(config, struct marvell_mac_priv,
                                                  phylink_config);

    /* Clear all supported modes first */
    linkmode_zero(supported);

    /* Add supported modes based on retimer configuration */
    if (priv->retimer_mode) {
        /* Retimer mode supports fixed speeds */
        if (priv->max_speed >= 25000) {
            linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseCR_Full_BIT, supported);
            linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseKR_Full_BIT, supported);
            linkmode_set_bit(ETHTOOL_LINK_MODE_25000baseSR_Full_BIT, supported);
        }
        linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseCR_Full_BIT, supported);
        linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseKR_Full_BIT, supported);
        linkmode_set_bit(ETHTOOL_LINK_MODE_10000baseSR_Full_BIT, supported);
    } else {
        /* Normal PHY mode - add standard gigabit modes */
        linkmode_set_bit(ETHTOOL_LINK_MODE_1000baseT_Full_BIT, supported);
        linkmode_set_bit(ETHTOOL_LINK_MODE_1000baseX_Full_BIT, supported);
    }

    /* Always support pause frames */
    linkmode_set_bit(ETHTOOL_LINK_MODE_Pause_BIT, supported);
    linkmode_set_bit(ETHTOOL_LINK_MODE_Asym_Pause_BIT, supported);

    /* Copy supported modes to advertising */
    linkmode_and(state->advertising, state->advertising, supported);
}

static void marvell_mac_phylink_mac_config(struct phylink_config *config,
                                           unsigned int mode,
                                           const struct phylink_link_state *state)
{
    struct marvell_mac_priv *priv = container_of(config, struct marvell_mac_priv,
                                                  phylink_config);
    u32 ctrl;

    /* Read current control register */
    ctrl = marvell_mac_readl(priv, MAC_CTRL_REG);

    /* Clear speed bits */
    ctrl &= ~MAC_CTRL_SPEED_MASK;

    /* Set speed based on state */
    switch (state->speed) {
    case SPEED_25000:
        ctrl |= MAC_CTRL_SPEED_25G;
        break;
    case SPEED_10000:
        ctrl |= MAC_CTRL_SPEED_10G;
        break;
    default:
        /* Default to 25G for retimer mode */
        if (priv->retimer_mode)
            ctrl |= MAC_CTRL_SPEED_25G;
        break;
    }

    /* Set duplex */
    if (state->duplex == DUPLEX_FULL)
        ctrl |= MAC_CTRL_DUPLEX_FULL;
    else
        ctrl &= ~MAC_CTRL_DUPLEX_FULL;

    /* Write back control register */
    marvell_mac_writel(priv, MAC_CTRL_REG, ctrl);

    dev_dbg(priv->dev, "MAC configured: speed=%d, duplex=%d, interface=%d\n",
            state->speed, state->duplex, state->interface);
}

static void marvell_mac_phylink_mac_link_down(struct phylink_config *config,
                                              unsigned int mode,
                                              phy_interface_t interface)
{
    struct marvell_mac_priv *priv = container_of(config, struct marvell_mac_priv,
                                                  phylink_config);

    /* Stop TX queue */
    netif_stop_queue(priv->netdev);

    dev_info(priv->dev, "Link down\n");
}

static void marvell_mac_phylink_mac_link_up(struct phylink_config *config,
                                            struct phy_device *phy,
                                            unsigned int mode,
                                            phy_interface_t interface,
                                            int speed, int duplex,
                                            bool tx_pause, bool rx_pause)
{
    struct marvell_mac_priv *priv = container_of(config, struct marvell_mac_priv,
                                                  phylink_config);

    /* Start TX queue */
    netif_start_queue(priv->netdev);

    dev_info(priv->dev, "Link up: speed=%d, duplex=%s, tx_pause=%s, rx_pause=%s\n",
             speed, duplex == DUPLEX_FULL ? "full" : "half",
             tx_pause ? "enabled" : "disabled",
             rx_pause ? "enabled" : "disabled");
}

/* Network device operations structure */
static const struct net_device_ops marvell_mac_netdev_ops = {
    .ndo_open = marvell_mac_open,
    .ndo_stop = marvell_mac_close,
    .ndo_start_xmit = marvell_mac_start_xmit,
    .ndo_get_stats = marvell_mac_get_stats,
    .ndo_set_mac_address = marvell_mac_set_mac_address,
    .ndo_validate_addr = eth_validate_addr,
};

/* Initialize phylink */
static int marvell_mac_phylink_init(struct marvell_mac_priv *priv)
{
    struct device_node *np = priv->dev->of_node;
    struct phylink *phylink;
    int ret;

    /* Setup phylink config */
    priv->phylink_config.dev = &priv->netdev->dev;
    priv->phylink_config.type = PHYLINK_NETDEV;

    /* Get PHY interface type from device tree */
    ret = of_get_phy_mode(np, &priv->phy_interface);
    if (ret) {
        /* Default to KR for retimer mode */
        priv->phy_interface = priv->retimer_mode ?
                              PHY_INTERFACE_MODE_25GBASE_KR :
                              PHY_INTERFACE_MODE_SGMII;
    }

    /* Create phylink instance */
    phylink = phylink_create(&priv->phylink_config, of_fwnode_handle(np),
                             priv->phy_interface, &marvell_mac_phylink_ops);
    if (IS_ERR(phylink)) {
        dev_err(priv->dev, "Failed to create phylink: %ld\n", PTR_ERR(phylink));
        return PTR_ERR(phylink);
    }

    priv->phylink = phylink;

    /* Connect to PHY if specified in device tree */
    ret = phylink_of_phy_connect(phylink, np, 0);
    if (ret && ret != -ENODEV) {
        dev_err(priv->dev, "Failed to connect to PHY: %d\n", ret);
        phylink_destroy(phylink);
        return ret;
    }

    dev_info(priv->dev, "Phylink initialized with interface %s\n",
             phy_modes(priv->phy_interface));

    return 0;
}

/* Cleanup phylink */
static void marvell_mac_phylink_cleanup(struct marvell_mac_priv *priv)
{
    if (priv->phylink) {
        phylink_disconnect_phy(priv->phylink);
        phylink_destroy(priv->phylink);
        priv->phylink = NULL;
    }
}

/* Platform driver probe function */
static int marvell_mac_probe(struct platform_device *pdev)
{
    struct device *dev = &pdev->dev;
    struct device_node *np = dev->of_node;
    struct marvell_mac_priv *priv;
    struct net_device *netdev;
    struct resource *res;
    int ret;

    /* Allocate network device */
    netdev = alloc_etherdev(sizeof(*priv));
    if (!netdev)
        return -ENOMEM;

    priv = netdev_priv(netdev);
    priv->netdev = netdev;
    priv->dev = dev;

    /* Get memory resource */
    res = platform_get_resource(pdev, IORESOURCE_MEM, 0);
    priv->base = devm_ioremap_resource(dev, res);
    if (IS_ERR(priv->base)) {
        ret = PTR_ERR(priv->base);
        goto err_free_netdev;
    }

    /* Get IRQ */
    priv->irq = platform_get_irq(pdev, 0);
    if (priv->irq < 0) {
        ret = priv->irq;
        goto err_free_netdev;
    }

    /* Get clocks */
    priv->mac_clk = devm_clk_get(dev, "mac");
    if (IS_ERR(priv->mac_clk)) {
        dev_err(dev, "Failed to get MAC clock\n");
        ret = PTR_ERR(priv->mac_clk);
        goto err_free_netdev;
    }

    priv->phy_clk = devm_clk_get_optional(dev, "phy");
    if (IS_ERR(priv->phy_clk)) {
        ret = PTR_ERR(priv->phy_clk);
        goto err_free_netdev;
    }

    /* Get reset control */
    priv->mac_rst = devm_reset_control_get_optional(dev, "mac");
    if (IS_ERR(priv->mac_rst)) {
        ret = PTR_ERR(priv->mac_rst);
        goto err_free_netdev;
    }

    /* Parse device tree properties */
    priv->retimer_mode = of_property_read_bool(np, "marvell,retimer-mode");

    ret = of_property_read_u32(np, "marvell,max-speed", &priv->max_speed);
    if (ret)
        priv->max_speed = priv->retimer_mode ? 25000 : 1000;

    /* Initialize locks */
    spin_lock_init(&priv->tx_lock);
    spin_lock_init(&priv->rx_lock);

    /* Setup network device */
    netdev->netdev_ops = &marvell_mac_netdev_ops;
    netdev->min_mtu = ETH_MIN_MTU;
    netdev->max_mtu = MAC_MAX_FRAME_SIZE - ETH_HLEN - ETH_FCS_LEN;

    /* Get MAC address from device tree or generate random */
    ret = of_get_mac_address(np, netdev->dev_addr);
    if (ret) {
        eth_hw_addr_random(netdev);
        dev_warn(dev, "Using random MAC address: %pM\n", netdev->dev_addr);
    }

    /* Set device features */
    netdev->features = NETIF_F_SG | NETIF_F_RXCSUM | NETIF_F_TXCSUM;
    netdev->hw_features = netdev->features;

    /* Initialize NAPI */
    netif_napi_add(netdev, &priv->napi, marvell_mac_poll, 64);

    /* Create work queue */
    priv->wq = create_singlethread_workqueue(dev_name(dev));
    if (!priv->wq) {
        ret = -ENOMEM;
        goto err_free_netdev;
    }

    INIT_DELAYED_WORK(&priv->link_work, marvell_mac_link_work);

    /* Initialize phylink */
    ret = marvell_mac_phylink_init(priv);
    if (ret)
        goto err_destroy_wq;

    /* Set device DMA parameters */
    ret = dma_set_mask_and_coherent(dev, DMA_BIT_MASK(32));
    if (ret) {
        dev_err(dev, "Failed to set DMA mask: %d\n", ret);
        goto err_phylink_cleanup;
    }

    /* Register network device */
    ret = register_netdev(netdev);
    if (ret) {
        dev_err(dev, "Failed to register network device: %d\n", ret);
        goto err_phylink_cleanup;
    }

    /* Store private data in platform device */
    platform_set_drvdata(pdev, priv);

    dev_info(dev, "Marvell 88x5113 MAC driver loaded (%s mode, max_speed=%u)\n",
             priv->retimer_mode ? "retimer" : "normal", priv->max_speed);

    return 0;

err_phylink_cleanup:
    marvell_mac_phylink_cleanup(priv);
err_destroy_wq:
    destroy_workqueue(priv->wq);
err_free_netdev:
    free_netdev(netdev);
    return ret;
}

/* Platform driver remove function */
static int marvell_mac_remove(struct platform_device *pdev)
{
    struct marvell_mac_priv *priv = platform_get_drvdata(pdev);
    struct net_device *netdev = priv->netdev;

    /* Unregister network device */
    unregister_netdev(netdev);

    /* Cleanup phylink */
    marvell_mac_phylink_cleanup(priv);

    /* Destroy work queue */
    destroy_workqueue(priv->wq);

    /* Free network device */
    free_netdev(netdev);

    dev_info(&pdev->dev, "Marvell 88x5113 MAC driver removed\n");

    return 0;
}

/* Device tree match table */
static const struct of_device_id marvell_mac_of_match[] = {
    { .compatible = "marvell,88x5113-mac" },
    { /* sentinel */ }
};
MODULE_DEVICE_TABLE(of, marvell_mac_of_match);

/* Platform driver structure */
static struct platform_driver marvell_mac_driver = {
    .probe = marvell_mac_probe,
    .remove = marvell_mac_remove,
    .driver = {
        .name = DRIVER_NAME,
        .of_match_table = marvell_mac_of_match,
    },
};

module_platform_driver(marvell_mac_driver);

MODULE_DESCRIPTION("Marvell 88x5113 MAC driver");
MODULE_AUTHOR("Linux Kernel Network Developers");
MODULE_LICENSE("GPL");
MODULE_VERSION(DRIVER_VERSION);