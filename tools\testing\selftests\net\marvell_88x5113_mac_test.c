// SPDX-License-Identifier: GPL-2.0
/*
 * Test program for Marvell 88x5113 MAC driver
 *
 * This program tests the integration between the MAC driver and
 * the 88x5113 PHY driver, including link establishment, data
 * transmission, and retimer mode operation.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <linux/ethtool.h>
#include <linux/sockios.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <errno.h>
#include <time.h>

#define TEST_INTERFACE "eth0"  /* Adjust as needed */
#define TEST_PACKET_SIZE 1500
#define TEST_PACKET_COUNT 1000

struct test_stats {
    int tests_run;
    int tests_passed;
    int tests_failed;
    char last_error[256];
};

/* Test MAC driver detection */
static int test_mac_driver_detection(const char *interface, struct test_stats *stats)
{
    int sock;
    struct ifreq ifr;
    struct ethtool_drvinfo drvinfo;
    int ret = 0;

    printf("Testing MAC driver detection...\n");

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "Failed to create socket: %s", strerror(errno));
        return -1;
    }

    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);

    memset(&drvinfo, 0, sizeof(drvinfo));
    drvinfo.cmd = ETHTOOL_GDRVINFO;
    ifr.ifr_data = (char *)&drvinfo;

    if (ioctl(sock, SIOCETHTOOL, &ifr) < 0) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "ETHTOOL_GDRVINFO failed: %s", strerror(errno));
        close(sock);
        return -1;
    }

    printf("  Driver: %s\n", drvinfo.driver);
    printf("  Version: %s\n", drvinfo.version);
    printf("  Bus info: %s\n", drvinfo.bus_info);

    /* Check if it's our MAC driver */
    if (strstr(drvinfo.driver, "88x5113") || strstr(drvinfo.driver, "marvell")) {
        printf("  ✓ Marvell 88x5113 MAC driver detected\n");
        ret = 0;
    } else {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "Expected Marvell 88x5113 MAC driver, got: %s", drvinfo.driver);
        ret = -1;
    }

    close(sock);
    return ret;
}

/* Test PHY integration */
static int test_phy_integration(const char *interface, struct test_stats *stats)
{
    int sock;
    struct ifreq ifr;
    struct ethtool_cmd cmd;
    int ret = 0;

    printf("Testing PHY integration...\n");

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "Failed to create socket: %s", strerror(errno));
        return -1;
    }

    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);

    memset(&cmd, 0, sizeof(cmd));
    cmd.cmd = ETHTOOL_GSET;
    ifr.ifr_data = (char *)&cmd;

    if (ioctl(sock, SIOCETHTOOL, &ifr) < 0) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "ETHTOOL_GSET failed: %s", strerror(errno));
        close(sock);
        return -1;
    }

    printf("  Speed: %u Mbps\n", ethtool_cmd_speed(&cmd));
    printf("  Duplex: %s\n", cmd.duplex == DUPLEX_FULL ? "Full" : "Half");
    printf("  Port: %s\n", cmd.port == PORT_FIBRE ? "Fiber" : "Other");

    /* Check for retimer mode speeds */
    u32 speed = ethtool_cmd_speed(&cmd);
    if (speed == 25000 || speed == 10000) {
        printf("  ✓ Retimer mode speed detected: %u Mbps\n", speed);
        ret = 0;
    } else if (speed == SPEED_UNKNOWN) {
        printf("  ⚠ Speed unknown (PHY may be initializing)\n");
        ret = 0;  /* Not necessarily an error */
    } else {
        printf("  ⚠ Unexpected speed: %u Mbps\n", speed);
        ret = 0;  /* Not necessarily an error */
    }

    close(sock);
    return ret;
}

/* Test link status monitoring */
static int test_link_status(const char *interface, struct test_stats *stats)
{
    char path[256];
    FILE *file;
    char buffer[64];
    int carrier, operstate_up;

    printf("Testing link status...\n");

    /* Check carrier status */
    snprintf(path, sizeof(path), "/sys/class/net/%s/carrier", interface);
    file = fopen(path, "r");
    if (!file) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "Failed to read carrier status: %s", strerror(errno));
        return -1;
    }

    if (fgets(buffer, sizeof(buffer), file)) {
        carrier = atoi(buffer);
        printf("  Carrier: %s\n", carrier ? "UP" : "DOWN");
    } else {
        carrier = 0;
    }
    fclose(file);

    /* Check operational state */
    snprintf(path, sizeof(path), "/sys/class/net/%s/operstate", interface);
    file = fopen(path, "r");
    if (!file) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "Failed to read operstate: %s", strerror(errno));
        return -1;
    }

    if (fgets(buffer, sizeof(buffer), file)) {
        /* Remove newline */
        buffer[strcspn(buffer, "\n")] = 0;
        printf("  Operstate: %s\n", buffer);
        operstate_up = (strcmp(buffer, "up") == 0);
    } else {
        operstate_up = 0;
    }
    fclose(file);

    if (carrier && operstate_up) {
        printf("  ✓ Link is UP\n");
        return 0;
    } else {
        printf("  ⚠ Link is DOWN (may be expected during initialization)\n");
        return 0;  /* Not necessarily an error during testing */
    }
}

/* Test interface configuration */
static int test_interface_config(const char *interface, struct test_stats *stats)
{
    int sock;
    struct ifreq ifr;
    int ret = 0;

    printf("Testing interface configuration...\n");

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "Failed to create socket: %s", strerror(errno));
        return -1;
    }

    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);

    /* Get interface flags */
    if (ioctl(sock, SIOCGIFFLAGS, &ifr) < 0) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "SIOCGIFFLAGS failed: %s", strerror(errno));
        close(sock);
        return -1;
    }

    printf("  Interface flags: 0x%x\n", ifr.ifr_flags);
    printf("  UP: %s\n", (ifr.ifr_flags & IFF_UP) ? "Yes" : "No");
    printf("  RUNNING: %s\n", (ifr.ifr_flags & IFF_RUNNING) ? "Yes" : "No");
    printf("  BROADCAST: %s\n", (ifr.ifr_flags & IFF_BROADCAST) ? "Yes" : "No");
    printf("  MULTICAST: %s\n", (ifr.ifr_flags & IFF_MULTICAST) ? "Yes" : "No");

    /* Get MTU */
    if (ioctl(sock, SIOCGIFMTU, &ifr) < 0) {
        snprintf(stats->last_error, sizeof(stats->last_error), 
                 "SIOCGIFMTU failed: %s", strerror(errno));
        close(sock);
        return -1;
    }

    printf("  MTU: %d\n", ifr.ifr_mtu);

    /* Check if MTU is reasonable for high-speed operation */
    if (ifr.ifr_mtu >= 1500 && ifr.ifr_mtu <= 9600) {
        printf("  ✓ MTU is within expected range\n");
    } else {
        printf("  ⚠ MTU may be outside expected range\n");
    }

    close(sock);
    return ret;
}

/* Test statistics */
static int test_statistics(const char *interface, struct test_stats *stats)
{
    char path[256];
    FILE *file;
    unsigned long long rx_packets, tx_packets, rx_bytes, tx_bytes;
    unsigned long long rx_errors, tx_errors, rx_dropped, tx_dropped;

    printf("Testing statistics...\n");

    /* Read various statistics */
    snprintf(path, sizeof(path), "/sys/class/net/%s/statistics/rx_packets", interface);
    file = fopen(path, "r");
    if (file) {
        fscanf(file, "%llu", &rx_packets);
        fclose(file);
    } else {
        rx_packets = 0;
    }

    snprintf(path, sizeof(path), "/sys/class/net/%s/statistics/tx_packets", interface);
    file = fopen(path, "r");
    if (file) {
        fscanf(file, "%llu", &tx_packets);
        fclose(file);
    } else {
        tx_packets = 0;
    }

    snprintf(path, sizeof(path), "/sys/class/net/%s/statistics/rx_bytes", interface);
    file = fopen(path, "r");
    if (file) {
        fscanf(file, "%llu", &rx_bytes);
        fclose(file);
    } else {
        rx_bytes = 0;
    }

    snprintf(path, sizeof(path), "/sys/class/net/%s/statistics/tx_bytes", interface);
    file = fopen(path, "r");
    if (file) {
        fscanf(file, "%llu", &tx_bytes);
        fclose(file);
    } else {
        tx_bytes = 0;
    }

    printf("  RX packets: %llu\n", rx_packets);
    printf("  TX packets: %llu\n", tx_packets);
    printf("  RX bytes: %llu\n", rx_bytes);
    printf("  TX bytes: %llu\n", tx_bytes);

    printf("  ✓ Statistics accessible\n");
    return 0;
}

/* Run all tests */
static void run_tests(const char *interface)
{
    struct test_stats stats = {0};
    int ret;

    printf("=== Marvell 88x5113 MAC Driver Test Suite ===\n");
    printf("Testing interface: %s\n\n", interface);

    /* Test 1: MAC driver detection */
    stats.tests_run++;
    ret = test_mac_driver_detection(interface, &stats);
    if (ret == 0) {
        stats.tests_passed++;
        printf("✓ PASSED: MAC driver detection\n\n");
    } else {
        stats.tests_failed++;
        printf("✗ FAILED: MAC driver detection - %s\n\n", stats.last_error);
    }

    /* Test 2: PHY integration */
    stats.tests_run++;
    ret = test_phy_integration(interface, &stats);
    if (ret == 0) {
        stats.tests_passed++;
        printf("✓ PASSED: PHY integration\n\n");
    } else {
        stats.tests_failed++;
        printf("✗ FAILED: PHY integration - %s\n\n", stats.last_error);
    }

    /* Test 3: Link status */
    stats.tests_run++;
    ret = test_link_status(interface, &stats);
    if (ret == 0) {
        stats.tests_passed++;
        printf("✓ PASSED: Link status\n\n");
    } else {
        stats.tests_failed++;
        printf("✗ FAILED: Link status - %s\n\n", stats.last_error);
    }

    /* Test 4: Interface configuration */
    stats.tests_run++;
    ret = test_interface_config(interface, &stats);
    if (ret == 0) {
        stats.tests_passed++;
        printf("✓ PASSED: Interface configuration\n\n");
    } else {
        stats.tests_failed++;
        printf("✗ FAILED: Interface configuration - %s\n\n", stats.last_error);
    }

    /* Test 5: Statistics */
    stats.tests_run++;
    ret = test_statistics(interface, &stats);
    if (ret == 0) {
        stats.tests_passed++;
        printf("✓ PASSED: Statistics\n\n");
    } else {
        stats.tests_failed++;
        printf("✗ FAILED: Statistics - %s\n\n", stats.last_error);
    }

    /* Print summary */
    printf("=== Test Summary ===\n");
    printf("Total tests: %d\n", stats.tests_run);
    printf("Passed: %d\n", stats.tests_passed);
    printf("Failed: %d\n", stats.tests_failed);

    if (stats.tests_failed == 0) {
        printf("✓ All tests passed!\n");
    } else {
        printf("✗ Some tests failed!\n");
    }
}

int main(int argc, char *argv[])
{
    const char *interface = TEST_INTERFACE;

    if (argc > 1) {
        interface = argv[1];
    }

    printf("Marvell 88x5113 MAC Driver Test\n");
    printf("Usage: %s [interface]\n", argv[0]);
    printf("Default interface: %s\n\n", TEST_INTERFACE);

    run_tests(interface);

    return 0;
}
