# SPDX-License-Identifier: GPL-2.0-only
#
# PHY Layer Configuration
#

config PHYLINK
	tristate
	depends on NETDEVICES
	select PHYLIB
	select SWPHY
	help
	  PHYlink models the link between the PHY and MAC, allowing fixed
	  configuration links, PHYs, and Serdes links with MAC level
	  autonegotiation modes.

menuconfig PH<PERSON>LIB
	tristate "PHY Device support and infrastructure"
	depends on NETDEVICES
	select MDIO_DEVICE
	select MDIO_DEVRES
	help
	  Ethernet controllers are usually attached to PHY
	  devices.  This option provides infrastructure for
	  managing PHY devices.

if PHYLIB

config SWPHY
	bool

config LED_TRIGGER_PHY
	bool "Support LED triggers for tracking link state"
	depends on LEDS_TRIGGERS
	help
	  Adds support for a set of LED trigger events per-PHY.  Link
	  state change will trigger the events, for consumption by an
	  LED class driver.  There are triggers for each link speed currently
	  supported by the PHY and also a one common "link" trigger as a
	  logical-or of all the link speed ones.
	  All these triggers are named according to the following pattern:
	      <mii bus id>:<phy>:<speed>

	  Where speed is in the form:
		<Speed in megabits>Mbps OR <Speed in gigabits>Gbps OR link
		for any speed known to the PHY.


config FIXED_PHY
	tristate "MDIO Bus/PHY emulation with fixed speed/link PHYs"
	depends on PHYLIB
	select SWPHY
	help
	  Adds the platform "fixed" MDIO Bus to cover the boards that use
	  PHYs that are not connected to the real MDIO bus.

	  Currently tested with mpc866ads and mpc8349e-mitx.

config SFP
	tristate "SFP cage support"
	depends on I2C && PHYLINK
	depends on HWMON || HWMON=n
	select MDIO_I2C

comment "MII PHY device drivers"

config AMD_PHY
	tristate "AMD PHYs"
	help
	  Currently supports the am79c874

config MESON_GXL_PHY
	tristate "Amlogic Meson GXL Internal PHY"
	depends on ARCH_MESON || COMPILE_TEST
	help
	  Currently has a driver for the Amlogic Meson GXL Internal PHY

config ADIN_PHY
	tristate "Analog Devices Industrial Ethernet PHYs"
	help
	  Adds support for the Analog Devices Industrial Ethernet PHYs.
	  Currently supports the:
	  - ADIN1200 - Robust,Industrial, Low Power 10/100 Ethernet PHY
	  - ADIN1300 - Robust,Industrial, Low Latency 10/100/1000 Gigabit
	    Ethernet PHY

config AQUANTIA_PHY
	tristate "Aquantia PHYs"
	help
	  Currently supports the Aquantia AQ1202, AQ2104, AQR105, AQR405

config AX88796B_PHY
	tristate "Asix PHYs"
	help
	  Currently supports the Asix Electronics PHY found in the X-Surf 100
	  AX88796B package.

config BROADCOM_PHY
	tristate "Broadcom 54XX PHYs"
	select BCM_NET_PHYLIB
	help
	  Currently supports the BCM5411, BCM5421, BCM5461, BCM54616S, BCM5464,
	  BCM5481, BCM54810 and BCM5482 PHYs.

config BCM54140_PHY
	tristate "Broadcom BCM54140 PHY"
	depends on PHYLIB
	depends on HWMON || HWMON=n
	select BCM_NET_PHYLIB
	help
	  Support the Broadcom BCM54140 Quad SGMII/QSGMII PHY.

	  This driver also supports the hardware monitoring of this PHY and
	  exposes voltage and temperature sensors.

config BCM63XX_PHY
	tristate "Broadcom 63xx SOCs internal PHY"
	depends on BCM63XX || COMPILE_TEST
	select BCM_NET_PHYLIB
	help
	  Currently supports the 6348 and 6358 PHYs.

config BCM7XXX_PHY
	tristate "Broadcom 7xxx SOCs internal PHYs"
	select BCM_NET_PHYLIB
	help
	  Currently supports the BCM7366, BCM7439, BCM7445, and
	  40nm and 65nm generation of BCM7xxx Set Top Box SoCs.

config BCM84881_PHY
	tristate "Broadcom BCM84881 PHY"
	depends on PHYLIB
	help
	  Support the Broadcom BCM84881 PHY.

config BCM87XX_PHY
	tristate "Broadcom BCM8706 and BCM8727 PHYs"
	help
	  Currently supports the BCM8706 and BCM8727 10G Ethernet PHYs.

config BCM_CYGNUS_PHY
	tristate "Broadcom Cygnus/Omega SoC internal PHY"
	depends on ARCH_BCM_IPROC || COMPILE_TEST
	depends on MDIO_BCM_IPROC
	select BCM_NET_PHYLIB
	help
	  This PHY driver is for the 1G internal PHYs of the Broadcom
	  Cygnus and Omega Family SoC.

	  Currently supports internal PHY's used in the BCM11300,
	  BCM11320, BCM11350, BCM11360, BCM58300, BCM58302,
	  BCM58303 & BCM58305 Broadcom Cygnus SoCs.

config BCM_NET_PHYLIB
	tristate

config CICADA_PHY
	tristate "Cicada PHYs"
	help
	  Currently supports the cis8204

config CORTINA_PHY
	tristate "Cortina EDC CDR 10G Ethernet PHY"
	help
	  Currently supports the CS4340 phy.

config DAVICOM_PHY
	tristate "Davicom PHYs"
	help
	  Currently supports dm9161e and dm9131

config ICPLUS_PHY
	tristate "ICPlus PHYs"
	help
	  Currently supports the IP175C and IP1001 PHYs.

config LXT_PHY
	tristate "Intel LXT PHYs"
	help
	  Currently supports the lxt970, lxt971

config INPHI_PHY
	tristate "Inphi CDR 10G/25G Ethernet PHY"
	help
	  Currently supports the IN112525_S03 part @ 25G

config INTEL_XWAY_PHY
	tristate "Intel XWAY PHYs"
	help
	  Supports the Intel XWAY (former Lantiq) 11G and 22E PHYs.
	  These PHYs are marked as standalone chips under the names
	  PEF 7061, PEF 7071 and PEF 7072 or integrated into the Intel
	  SoCs xRX200, xRX300, xRX330, xRX350 and xRX550.

config LSI_ET1011C_PHY
	tristate "LSI ET1011C PHY"
	help
	  Supports the LSI ET1011C PHY.

config MARVELL_PHY
	tristate "Marvell Alaska PHYs"
	help
	  Currently has a driver for the 88E1XXX

config MARVELL_10G_PHY
	tristate "Marvell Alaska 10Gbit PHYs"
	help
	  Support for the Marvell Alaska MV88X3310 and compatible PHYs.

config MARVELL_88X2222_PHY
	tristate "Marvell 88X2222 PHY"
	help
	  Support for the Marvell 88X2222 Dual-port Multi-speed Ethernet
	  Transceiver.

config MARVELL_88X5113_PHY
	tristate "Marvell 88x5113 PHY"
	depends on I2C
	help
	  Support for the Marvell 88x5113 PHY in retimer mode.
	  This PHY communicates via I2C instead of traditional MDIO
	  when MDIO/MDC pins are connected to CPLD. Supports 10G and
	  25G retimer operation with up to 4 lanes.

config MAXLINEAR_GPHY
	tristate "Maxlinear Ethernet PHYs"
	help
	  Support for the Maxlinear GPY115, GPY211, GPY212, GPY215,
	  GPY241, GPY245 PHYs.

config MEDIATEK_GE_PHY
	tristate "MediaTek Gigabit Ethernet PHYs"
	help
	  Supports the MediaTek Gigabit Ethernet PHYs.

config MICREL_PHY
	tristate "Micrel PHYs"
	help
	  Supports the KSZ9021, VSC8201, KS8001 PHYs.

config MICROCHIP_PHY
	tristate "Microchip PHYs"
	help
	  Supports the LAN88XX PHYs.

config MICROCHIP_T1_PHY
	tristate "Microchip T1 PHYs"
	help
	  Supports the LAN87XX PHYs.

config MICROSEMI_PHY
	tristate "Microsemi PHYs"
	depends on MACSEC || MACSEC=n
	depends on PTP_1588_CLOCK_OPTIONAL || !NETWORK_PHY_TIMESTAMPING
	select CRYPTO_LIB_AES if MACSEC
	help
	  Currently supports VSC8514, VSC8530, VSC8531, VSC8540 and VSC8541 PHYs

config MOTORCOMM_PHY
	tristate "Motorcomm PHYs"
	help
	  Enables support for Motorcomm network PHYs.
	  Currently supports the YT8511 gigabit PHY.

config NATIONAL_PHY
	tristate "National Semiconductor PHYs"
	help
	  Currently supports the DP83865 PHY.

config NXP_C45_TJA11XX_PHY
	tristate "NXP C45 TJA11XX PHYs"
	depends on PTP_1588_CLOCK_OPTIONAL
	help
	  Enable support for NXP C45 TJA11XX PHYs.
	  Currently supports only the TJA1103 PHY.

config NXP_TJA11XX_PHY
	tristate "NXP TJA11xx PHYs support"
	depends on HWMON
	help
	  Currently supports the NXP TJA1100 and TJA1101 PHY.

config AT803X_PHY
	tristate "Qualcomm Atheros AR803X PHYs and QCA833x PHYs"
	depends on REGULATOR
	help
	  Currently supports the AR8030, AR8031, AR8033, AR8035 and internal
	  QCA8337(Internal qca8k PHY) model

config QSEMI_PHY
	tristate "Quality Semiconductor PHYs"
	help
	  Currently supports the qs6612

config REALTEK_PHY
	tristate "Realtek PHYs"
	help
	  Supports the Realtek 821x PHY.

config RENESAS_PHY
	tristate "Renesas PHYs"
	help
	  Supports the Renesas PHYs uPD60620 and uPD60620A.

config ROCKCHIP_PHY
	tristate "Rockchip Ethernet PHYs"
	help
	  Currently supports the integrated Ethernet PHY.

config SMSC_PHY
	tristate "SMSC PHYs"
	help
	  Currently supports the LAN83C185, LAN8187 and LAN8700 PHYs

config STE10XP
	tristate "STMicroelectronics STe10Xp PHYs"
	help
	  This is the driver for the STe100p and STe101p PHYs.

config TERANETICS_PHY
	tristate "Teranetics PHYs"
	help
	  Currently supports the Teranetics TN2020

config DP83822_PHY
	tristate "Texas Instruments DP83822/825/826 PHYs"
	help
	  Supports the DP83822, DP83825I, DP83825CM, DP83825CS, DP83825S,
	  DP83826C and DP83826NC PHYs.

config DP83TC811_PHY
	tristate "Texas Instruments DP83TC811 PHY"
	help
	  Supports the DP83TC811 PHY.

config DP83848_PHY
	tristate "Texas Instruments DP83848 PHY"
	help
	  Supports the DP83848 PHY.

config DP83867_PHY
	tristate "Texas Instruments DP83867 Gigabit PHY"
	help
	  Currently supports the DP83867 PHY.

config DP83869_PHY
	tristate "Texas Instruments DP83869 Gigabit PHY"
	help
	  Currently supports the DP83869 PHY.  This PHY supports copper and
	  fiber connections.

config VITESSE_PHY
	tristate "Vitesse PHYs"
	help
	  Currently supports the vsc8244

config XILINX_GMII2RGMII
	tristate "Xilinx GMII2RGMII converter driver"
	help
	  This driver support xilinx GMII to RGMII IP core it provides
	  the Reduced Gigabit Media Independent Interface(RGMII) between
	  Ethernet physical media devices and the Gigabit Ethernet controller.

endif # PHYLIB

config MICREL_KS8995MA
	tristate "Micrel KS8995MA 5-ports 10/100 managed Ethernet switch"
	depends on SPI
