.. SPDX-License-Identifier: GPL-2.0

===========================================
DPAA2 Ethernet and Marvell 88x5113 PHY Integration
===========================================

Overview
========

This document describes the integration between the Freescale DPAA2 Ethernet
driver and the Marvell 88x5113 PHY driver. This integration replaces the
previous Inphi PHY driver support in DPAA2 systems, providing enhanced
retimer mode operation for high-speed Ethernet applications.

Architecture
============

The integration follows the standard Linux networking architecture with
DPAA2-specific enhancements:

::

    ┌─────────────────────────────────────────────────────────────┐
    │                    Linux Network Stack                     │
    ├─────────────────────────────────────────────────────────────┤
    │  eth0 (DPNI1)   │    eth1 (DPNI2)    │    eth2 (DPNI3)    │
    │  ┌─────────────┐ │  ┌─────────────┐  │  ┌─────────────┐    │
    │  │ DPAA2 ETH   │ │  │ DPAA2 ETH   │  │  │ DPAA2 ETH   │    │
    │  │ Driver      │ │  │ Driver      │  │  │ Driver      │    │
    │  └─────────────┘ │  └─────────────┘  │  └─────────────┘    │
    ├─────────────────────────────────────────────────────────────┤
    │  DPMAC1         │    DPMAC2         │    DPMAC3           │
    │  ┌─────────────┐ │  ┌─────────────┐  │  ┌─────────────┐    │
    │  │ DPAA2 MAC   │ │  │ DPAA2 MAC   │  │  │ DPAA2 MAC   │    │
    │  │ Driver      │ │  │ Driver      │  │  │ Driver      │    │
    │  └─────────────┘ │  └─────────────┘  │  └─────────────┘    │
    ├─────────────────────────────────────────────────────────────┤
    │                      Phylink Framework                     │
    ├─────────────────────────────────────────────────────────────┤
    │                   88x5113 PHY Driver                       │
    │                    (Retimer Mode)                          │
    ├─────────────────────────────────────────────────────────────┤
    │                     I2C Subsystem                          │
    │                    (Address 0x77)                          │
    └─────────────────────────────────────────────────────────────┘

Key Features
============

* **Seamless Integration**: Direct replacement of Inphi PHY driver
* **Retimer Mode Support**: Optimized for 25G and 10G retimer operation
* **Multi-MAC Support**: Single 88x5113 PHY serving multiple DPAA2 MACs
* **I2C Communication**: Uses I2C instead of traditional MDIO
* **Automatic Configuration**: Dynamic speed and lane configuration
* **Link State Synchronization**: Real-time link status updates

Integration Points
==================

DPAA2 MAC Driver Changes
-------------------------

The DPAA2 MAC driver (`dpaa2-mac.c`) has been enhanced with:

1. **88x5113 Detection**: Automatic detection of 88x5113 PHY
2. **Retimer Configuration**: Automatic retimer mode setup
3. **Speed Negotiation**: Support for 25G and 10G speeds
4. **Link Callbacks**: Integration with 88x5113 link state changes
5. **Interface Support**: Added 25GBASE-R interface mode

88x5113 PHY Driver Integration
------------------------------

The 88x5113 PHY driver provides MAC integration functions:

* ``mxd_88x5113_init_for_mac()`` - Initialize PHY for MAC integration
* ``mxd_88x5113_configure_retimer()`` - Configure retimer mode
* ``mxd_88x5113_register_mac_callback()`` - Register link state callback
* ``mxd_88x5113_is_link_up()`` - Check link status
* ``mxd_88x5113_recover_lanes()`` - Trigger lane recovery

Device Tree Configuration
=========================

Basic DPAA2 Configuration
--------------------------

::

    fsl_mc: fsl-mc@80c000000 {
        compatible = "fsl,qoriq-mc";
        
        dpmacs {
            dpmac@1 {
                compatible = "fsl,qoriq-mc-dpmac";
                reg = <1>;
                phy-handle = <&phy1>;
                phy-connection-type = "25gbase-kr";
                marvell,88x5113-retimer-mode;
            };
        };
    };

88x5113 PHY Configuration
-------------------------

::

    emdio1: mdio@8b96000 {
        phy1: phy@1 {
            compatible = "marvell,88x5113";
            reg = <1>;
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,max-speed = <25000>;
        };
    };

I2C Configuration
-----------------

::

    i2c1: i2c@2000000 {
        compatible = "fsl,vf610-i2c";
        reg = <0x0 0x2000000 0x0 0x10000>;
        clock-frequency = <400000>;
        
        phy_88x5113_i2c: phy@77 {
            compatible = "marvell,88x5113-i2c";
            reg = <0x77>;
        };
    };

Configuration Options
=====================

Kernel Configuration
---------------------

Enable the following kernel options:

::

    CONFIG_FSL_DPAA2_ETH=y
    CONFIG_MARVELL_88X5113_PHY=y
    CONFIG_PHYLINK=y
    CONFIG_I2C=y

Device Tree Properties
----------------------

**DPMAC Properties:**

* ``marvell,88x5113-retimer-mode`` - Enable 88x5113 retimer mode
* ``marvell,max-speed`` - Maximum speed (10000 or 25000)
* ``phy-connection-type`` - Interface type ("25gbase-kr" or "10gbase-kr")

**88x5113 PHY Properties:**

* ``marvell,i2c-bus`` - I2C bus number
* ``marvell,i2c-address`` - I2C device address (default: 0x77)
* ``marvell,retimer-mode`` - Enable retimer mode
* ``marvell,lane-count`` - Number of lanes (1-4)

Runtime Operation
=================

Initialization Sequence
------------------------

1. DPAA2 Ethernet driver loads and discovers DPMACs
2. DPAA2 MAC driver connects to phylink
3. 88x5113 PHY driver is detected and initialized
4. Retimer mode is configured based on device tree
5. Link monitoring begins with automatic recovery

Link State Management
---------------------

1. **Link Detection**: 88x5113 monitors lane status continuously
2. **State Changes**: Link state changes trigger MAC callbacks
3. **Speed Negotiation**: Automatic speed configuration (25G/10G)
4. **Recovery**: Automatic lane recovery on link failures

Performance Considerations
==========================

* **Polling Interval**: Default 2.5 seconds for lane monitoring
* **Lane Recovery**: Automatic recovery minimizes downtime
* **I2C Communication**: Optimized with retry mechanisms
* **Multi-MAC Efficiency**: Shared PHY resources across MACs

Monitoring and Debugging
========================

Interface Status
----------------

Check interface status::

    ip link show eth0
    ethtool eth0

Link Speed Verification::

    ethtool eth0 | grep Speed
    # Should show: Speed: 25000Mb/s

Driver Information::

    ethtool -i eth0
    # Should show DPAA2 driver with 88x5113 PHY

PHY Status Monitoring
---------------------

Check PHY detection::

    dmesg | grep 88x5113
    # Should show PHY initialization messages

Lane Status::

    cat /sys/class/net/eth0/carrier
    cat /sys/class/net/eth0/operstate

Debug Information::

    echo 8 > /proc/sys/kernel/printk
    dmesg | grep -E "(88x5113|dpaa2|retimer)"

Testing
=======

Integration Test Suite
----------------------

A comprehensive test program is provided::

    # Compile the test
    gcc -o dpaa2_88x5113_test tools/testing/selftests/net/dpaa2_88x5113_integration_test.c

    # Run the test
    ./dpaa2_88x5113_test

The test verifies:

* DPAA2 interface detection
* 88x5113 PHY integration
* Retimer mode operation
* Link status and speed
* Driver compatibility

Expected Results
----------------

Successful integration should show:

* DPAA2 interfaces detected
* 88x5113 PHY recognized
* 25G or 10G speed reported
* Retimer mode active
* Link status updates

Troubleshooting
===============

Common Issues
-------------

1. **PHY Not Detected**
   - Verify I2C bus configuration
   - Check I2C address (default 0x77)
   - Ensure 88x5113 PHY driver is loaded

2. **No Link Established**
   - Check lane configuration
   - Verify VCO codes
   - Monitor lane recovery process

3. **Speed Issues**
   - Confirm retimer mode configuration
   - Check device tree speed settings
   - Verify DPMAC interface type

4. **Driver Loading Issues**
   - Ensure CONFIG_MARVELL_88X5113_PHY=y
   - Check device tree compatibility strings
   - Verify DPAA2 framework is loaded

Debug Steps
-----------

1. **Check Driver Loading**::

    lsmod | grep -E "(dpaa2|88x5113)"
    dmesg | grep -E "(dpaa2|88x5113)"

2. **Verify Device Tree**::

    ls /proc/device-tree/fsl-mc/dpmacs/
    cat /proc/device-tree/emdio*/phy*/compatible

3. **Monitor I2C Communication**::

    i2cdetect -y 1  # Check if device at 0x77 is detected

4. **Check Interface Configuration**::

    ethtool -i eth0
    ethtool eth0

Migration from Inphi PHY
========================

To migrate from Inphi PHY to 88x5113:

1. **Update Device Tree**: Replace Inphi PHY nodes with 88x5113 configuration
2. **Kernel Configuration**: Enable 88x5113 PHY driver
3. **I2C Setup**: Configure I2C bus for 88x5113 communication
4. **Testing**: Verify functionality with integration test suite

The migration is designed to be seamless with minimal configuration changes
required in the DPAA2 Ethernet driver.

References
==========

* DPAA2 Ethernet driver documentation
* Marvell 88x5113 PHY driver documentation
* Linux phylink framework documentation
* Freescale DPAA2 architecture guide
