# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/net/marvell,88x5113-mac.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell 88x5113 MAC Controller

maintainers:
  - Linux Kernel Network Developers <<EMAIL>>

description: |
  The Marvell 88x5113 MAC controller provides Ethernet MAC functionality
  for systems using the Marvell 88x5113 PHY in retimer mode. It supports
  high-speed Ethernet operation up to 25G.

allOf:
  - $ref: ethernet-controller.yaml#

properties:
  compatible:
    enum:
      - marvell,88x5113-mac

  reg:
    maxItems: 1
    description: MAC controller register base address and size

  interrupts:
    maxItems: 1
    description: MAC controller interrupt

  clocks:
    minItems: 1
    maxItems: 2
    description: |
      Clock specifiers for MAC and PHY clocks.
      - First clock is the MAC clock (required)
      - Second clock is the PHY clock (optional)

  clock-names:
    minItems: 1
    maxItems: 2
    items:
      - const: mac
      - const: phy

  resets:
    maxItems: 1
    description: MAC controller reset

  reset-names:
    items:
      - const: mac

  phy-handle:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: |
      Phandle to the PHY device connected to this MAC controller.
      Should point to the 88x5113 PHY node.

  phy-mode:
    description: |
      PHY interface mode. For retimer mode, typically uses:
      - 25gbase-kr for 25G operation
      - 10gbase-kr for 10G operation
      - sgmii for 1G operation

  marvell,retimer-mode:
    type: boolean
    description: |
      Enable retimer mode operation. When set, the MAC operates
      with the 88x5113 PHY in retimer mode.

  marvell,max-speed:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Maximum speed supported by the MAC in Mbps.
      Valid values: 1000, 10000, 25000
      Default is 25000 for retimer mode, 1000 for normal mode.
    enum: [1000, 10000, 25000]

  local-mac-address:
    description: MAC address to be assigned to the controller

  mac-address:
    description: MAC address to be assigned to the controller

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/qcom,gcc-sm8250.h>
    #include <dt-bindings/reset/qcom,gcc-sm8250.h>

    ethernet@1c30000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c30000 0x10000>;
        interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
        
        clocks = <&gcc GCC_ETH_MAC_CLK>,
                 <&gcc GCC_ETH_PHY_CLK>;
        clock-names = "mac", "phy";
        
        resets = <&gcc GCC_ETH_MAC_RESET>;
        reset-names = "mac";
        
        phy-handle = <&phy0>;
        phy-mode = "25gbase-kr";
        
        marvell,retimer-mode;
        marvell,max-speed = <25000>;
        
        local-mac-address = [00 11 22 33 44 55];
    };

  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/qcom,gcc-sm8250.h>

    /* Example with MDIO bus and PHY definition */
    ethernet@1c40000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c40000 0x10000>;
        interrupts = <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
        
        clocks = <&gcc GCC_ETH_MAC_CLK>;
        clock-names = "mac";
        
        phy-handle = <&phy1>;
        phy-mode = "10gbase-kr";
        
        marvell,retimer-mode;
        marvell,max-speed = <10000>;
    };

    mdio {
        #address-cells = <1>;
        #size-cells = <0>;

        phy0: phy@0 {
            compatible = "marvell,88x5113";
            reg = <0>;
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <4>;
        };

        phy1: phy@1 {
            compatible = "marvell,88x5113";
            reg = <1>;
            marvell,i2c-bus = <2>;
            marvell,i2c-address = <0x77>;
            marvell,retimer-mode;
            marvell,lane-count = <2>;
        };
    };

  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/qcom,gcc-sm8250.h>

    /* Example for multiple MAC instances */
    ethernet@1c50000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c50000 0x10000>;
        interrupts = <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>;
        
        clocks = <&gcc GCC_ETH_MAC2_CLK>;
        clock-names = "mac";
        
        phy-handle = <&phy2>;
        phy-mode = "25gbase-kr";
        
        marvell,retimer-mode;
        marvell,max-speed = <25000>;
    };

    ethernet@1c60000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x1c60000 0x10000>;
        interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
        
        clocks = <&gcc GCC_ETH_MAC3_CLK>;
        clock-names = "mac";
        
        phy-handle = <&phy3>;
        phy-mode = "25gbase-kr";
        
        marvell,retimer-mode;
        marvell,max-speed = <25000>;
    };

    mdio {
        #address-cells = <1>;
        #size-cells = <0>;

        phy2: phy@2 {
            compatible = "marvell,88x5113";
            reg = <2>;
            marvell,i2c-bus = <1>;
            marvell,retimer-mode;
        };

        phy3: phy@3 {
            compatible = "marvell,88x5113";
            reg = <3>;
            marvell,i2c-bus = <1>;
            marvell,retimer-mode;
        };
    };
