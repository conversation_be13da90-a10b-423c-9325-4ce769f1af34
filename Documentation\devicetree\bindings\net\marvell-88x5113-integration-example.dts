// SPDX-License-Identifier: GPL-2.0
/*
 * Device Tree example for Marvell 88x5113 MAC and PHY integration
 *
 * This example shows how to configure a system with 3 MAC controllers
 * connected to a single 88x5113 PHY in retimer mode, as mentioned in
 * the requirements.
 */

/dts-v1/;

/ {
    model = "Marvell 88x5113 Integration Example";
    compatible = "marvell,88x5113-example";

    #address-cells = <2>;
    #size-cells = <2>;

    /* Clock definitions */
    clocks {
        #address-cells = <1>;
        #size-cells = <0>;

        /* System clocks */
        sys_clk: sys_clk {
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <100000000>; /* 100 MHz */
        };

        /* MAC clocks derived from system clock */
        mac1_clk: mac1_clk {
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <250000000>; /* 250 MHz for 25G */
        };

        mac2_clk: mac2_clk {
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <250000000>; /* 250 MHz for 25G */
        };

        mac3_clk: mac3_clk {
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <250000000>; /* 250 MHz for 25G */
        };

        phy_clk: phy_clk {
            compatible = "fixed-clock";
            #clock-cells = <0>;
            clock-frequency = <156250000>; /* 156.25 MHz for PHY */
        };
    };

    /* I2C bus for PHY communication */
    i2c1: i2c@1c000000 {
        compatible = "generic,i2c";
        reg = <0x0 0x1c000000 0x0 0x1000>;
        #address-cells = <1>;
        #size-cells = <0>;
        clock-frequency = <400000>; /* 400 kHz */

        /* 88x5113 PHY on I2C bus */
        phy_88x5113: phy@77 {
            compatible = "marvell,88x5113-i2c";
            reg = <0x77>;
            /* This represents the I2C device for direct communication */
        };
    };

    /* MDIO bus for PHY registration */
    mdio: mdio@1c010000 {
        compatible = "generic,mdio";
        reg = <0x0 0x1c010000 0x0 0x1000>;
        #address-cells = <1>;
        #size-cells = <0>;

        /* 88x5113 PHY - MDIO registration but I2C communication */
        phy0: phy@0 {
            compatible = "marvell,88x5113";
            reg = <0>;
            
            /* I2C configuration for actual communication */
            marvell,i2c-bus = <1>;
            marvell,i2c-address = <0x77>;
            
            /* Retimer mode configuration */
            marvell,retimer-mode;
            marvell,lane-count = <4>;
            marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
            marvell,poll-interval = <2500>;
            
            /* Maximum speed for retimer mode */
            max-speed = <25000>;
        };
    };

    /* MAC Controller 1 */
    ethernet@1c100000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x0 0x1c100000 0x0 0x10000>;
        interrupts = <0 100 4>; /* GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH */
        
        /* Clock configuration */
        clocks = <&mac1_clk>, <&phy_clk>;
        clock-names = "mac", "phy";
        
        /* Reset control (optional) */
        resets = <&reset_controller 10>;
        reset-names = "mac";
        
        /* PHY connection */
        phy-handle = <&phy0>;
        phy-mode = "25gbase-kr";
        
        /* Retimer mode configuration */
        marvell,retimer-mode;
        marvell,max-speed = <25000>;
        
        /* MAC address */
        local-mac-address = [00 11 22 33 44 55];
        
        /* Network device features */
        status = "okay";
    };

    /* MAC Controller 2 */
    ethernet@1c200000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x0 0x1c200000 0x0 0x10000>;
        interrupts = <0 101 4>; /* GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH */
        
        /* Clock configuration */
        clocks = <&mac2_clk>, <&phy_clk>;
        clock-names = "mac", "phy";
        
        /* Reset control (optional) */
        resets = <&reset_controller 11>;
        reset-names = "mac";
        
        /* PHY connection - same PHY, different logical connection */
        phy-handle = <&phy0>;
        phy-mode = "25gbase-kr";
        
        /* Retimer mode configuration */
        marvell,retimer-mode;
        marvell,max-speed = <25000>;
        
        /* MAC address */
        local-mac-address = [00 11 22 33 44 56];
        
        /* Network device features */
        status = "okay";
    };

    /* MAC Controller 3 */
    ethernet@1c300000 {
        compatible = "marvell,88x5113-mac";
        reg = <0x0 0x1c300000 0x0 0x10000>;
        interrupts = <0 102 4>; /* GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH */
        
        /* Clock configuration */
        clocks = <&mac3_clk>, <&phy_clk>;
        clock-names = "mac", "phy";
        
        /* Reset control (optional) */
        resets = <&reset_controller 12>;
        reset-names = "mac";
        
        /* PHY connection - same PHY, different logical connection */
        phy-handle = <&phy0>;
        phy-mode = "25gbase-kr";
        
        /* Retimer mode configuration */
        marvell,retimer-mode;
        marvell,max-speed = <25000>;
        
        /* MAC address */
        local-mac-address = [00 11 22 33 44 57];
        
        /* Network device features */
        status = "okay";
    };

    /* Reset controller (example) */
    reset_controller: reset@1c400000 {
        compatible = "generic,reset";
        reg = <0x0 0x1c400000 0x0 0x1000>;
        #reset-cells = <1>;
    };

    /* Memory configuration */
    memory@80000000 {
        device_type = "memory";
        reg = <0x0 0x80000000 0x0 0x40000000>; /* 1GB RAM */
    };

    /* CPU configuration */
    cpus {
        #address-cells = <1>;
        #size-cells = <0>;

        cpu@0 {
            device_type = "cpu";
            compatible = "arm,cortex-a53";
            reg = <0>;
        };
    };

    /* Interrupt controller */
    gic: interrupt-controller@1c410000 {
        compatible = "arm,gic-400";
        #interrupt-cells = <3>;
        interrupt-controller;
        reg = <0x0 0x1c410000 0x0 0x1000>,
              <0x0 0x1c420000 0x0 0x2000>;
    };

    /* Aliases for network interfaces */
    aliases {
        ethernet0 = &ethernet@1c100000;
        ethernet1 = &ethernet@1c200000;
        ethernet2 = &ethernet@1c300000;
    };

    /* Chosen configuration */
    chosen {
        bootargs = "console=ttyS0,115200 root=/dev/ram0 rw";
        stdout-path = "serial0:115200n8";
    };
};

/*
 * Integration Notes:
 * 
 * 1. Single 88x5113 PHY (phy0) serves three MAC controllers
 * 2. PHY uses I2C communication (address 0x77 on bus 1)
 * 3. All MACs operate in retimer mode at 25G
 * 4. Each MAC has its own clock domain and reset control
 * 5. PHY driver handles lane management across all connections
 * 6. MAC drivers integrate via phylink for seamless operation
 * 
 * Expected Network Interfaces:
 * - eth0: First MAC (1c100000)
 * - eth1: Second MAC (1c200000)  
 * - eth2: Third MAC (1c300000)
 * 
 * All interfaces will show 25G speed when link is established
 * through the 88x5113 retimer.
 */
