# LX2160A DPAA2 Ethernet and Marvell 88x5113 PHY Integration

## Overview

This document describes the complete integration of the Marvell 88x5113 PHY with the NXP LX2160A DPAA2 Ethernet system, replacing the previous Inphi PHY configuration. The integration provides high-speed 25G Ethernet connectivity through retimer mode operation.

## Hardware Configuration

### LX2160A RDB Board Setup

The LX2160A Reference Design Board (RDB) has been configured with:

- **Single 88x5113 PHY**: One physical device serving multiple MAC controllers
- **I2C Communication**: PHY uses I2C bus 1 at address 0x77
- **Three DPAA2 MACs**: DPMAC5, DPMAC6, and DPM<PERSON>7 connected to 88x5113
- **25G Operation**: All interfaces configured for 25GBASE-KR

### Physical Connections

```
┌─────────────────────────────────────────────────────────────┐
│                    LX2160A SoC                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   DPMAC5    │  │   DPMAC6    │  │   DPMAC7    │         │
│  │   (eth4)    │  │   (eth5)    │  │   (eth6)    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │                 │                 │              │
│         └─────────────────┼─────────────────┘              │
│                           │                                │
│  ┌─────────────────────────────────────────────────────────┤
│  │                    I2C Bus 1                           │
│  │                  (Address 0x77)                        │
│  └─────────────────────────────────────────────────────────┤
└─────────────────────────────────────────────────────────────┘
                              │
                              │ I2C Communication
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Marvell 88x5113 PHY                        │
│                   (Retimer Mode)                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Lane 0    │  │   Lane 1    │  │   Lane 2    │  Lane 3 │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## Device Tree Configuration

### Key Files Modified

1. **`arch/arm64/boot/dts/freescale/fsl-lx2160a.dtsi`**
   - Added `emdio_88x5113` MDIO bus for PHY registration

2. **`arch/arm64/boot/dts/freescale/fsl-lx2160a-rdb.dts`**
   - Replaced Inphi PHY configuration with 88x5113
   - Added I2C device configuration
   - Updated DPMAC connections

### DPMAC Configuration

```dts
&dpmac5 {
    phy-handle = <&phy_88x5113_1>;
    phy-connection-type = "25gbase-kr";
    managed = "in-band-status";
};

&dpmac6 {
    phy-handle = <&phy_88x5113_2>;
    phy-connection-type = "25gbase-kr";
    managed = "in-band-status";
};

&dpmac7 {
    phy-handle = <&phy_88x5113_3>;
    phy-connection-type = "25gbase-kr";
    managed = "in-band-status";
};
```

### 88x5113 PHY Configuration

```dts
&emdio_88x5113 {
    status = "okay";

    phy_88x5113_1: ethernet-phy@1 {
        compatible = "marvell,88x5113";
        reg = <0x1>;
        marvell,i2c-bus = <1>;
        marvell,i2c-address = <0x77>;
        marvell,retimer-mode;
        marvell,lane-count = <4>;
        marvell,max-speed = <25000>;
        marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
        marvell,lane-assignment = <0 1>;
    };
    
    /* Similar configuration for phy_88x5113_2 and phy_88x5113_3 */
};
```

### I2C Device Configuration

```dts
&i2c1 {
    status = "okay";

    phy_88x5113_i2c: phy@77 {
        compatible = "marvell,88x5113-i2c";
        reg = <0x77>;
        marvell,device-id = <0x5113>;
        marvell,retimer-mode;
        marvell,max-lanes = <4>;
        marvell,vco-codes = <0x0186 0x0186 0x0186 0x0186>;
    };
};
```

## Network Interface Mapping

After successful integration, the following network interfaces are available:

| Interface | DPMAC | PHY Type | Speed | Description |
|-----------|-------|----------|-------|-------------|
| eth0 | DPMAC17 | RGMII | 1G | Copper Ethernet |
| eth1 | DPMAC18 | RGMII | 1G | Copper Ethernet |
| eth2 | DPMAC3 | USXGMII | 10G | Aquantia PHY |
| eth3 | DPMAC4 | USXGMII | 10G | Aquantia PHY |
| **eth4** | **DPMAC5** | **88x5113** | **25G** | **Retimer Mode** |
| **eth5** | **DPMAC6** | **88x5113** | **25G** | **Retimer Mode** |
| **eth6** | **DPMAC7** | **88x5113** | **25G** | **Retimer Mode** |

## Kernel Configuration

### Required Kernel Options

```bash
CONFIG_FSL_DPAA2_ETH=y
CONFIG_MARVELL_88X5113_PHY=y
CONFIG_PHYLINK=y
CONFIG_I2C=y
CONFIG_I2C_IMX=y
```

### Module Loading

The following modules should be loaded:
```bash
modprobe fsl_dpaa2_eth
modprobe marvell_88x5113
modprobe i2c-imx
```

## Runtime Verification

### Check Interface Status

```bash
# List all network interfaces
ip link show

# Check 88x5113 interfaces specifically
ip link show eth4 eth5 eth6

# Verify speed configuration
ethtool eth4 | grep Speed
ethtool eth5 | grep Speed
ethtool eth6 | grep Speed
```

Expected output:
```
Speed: 25000Mb/s
```

### Check PHY Driver Loading

```bash
# Check if 88x5113 driver is loaded
lsmod | grep 88x5113

# Check driver information
ethtool -i eth4
ethtool -i eth5
ethtool -i eth6
```

### Monitor Link Status

```bash
# Check link status
cat /sys/class/net/eth4/carrier
cat /sys/class/net/eth5/carrier
cat /sys/class/net/eth6/carrier

# Monitor link changes
ip monitor link
```

### Check I2C Communication

```bash
# Scan I2C bus 1 for device at 0x77
i2cdetect -y 1

# Should show device at address 0x77
```

### Check Kernel Messages

```bash
# Check for 88x5113 initialization messages
dmesg | grep -i 88x5113

# Check for DPAA2 integration messages
dmesg | grep -i "dpaa2.*88x5113"

# Check for retimer mode messages
dmesg | grep -i retimer
```

## Testing and Validation

### Basic Connectivity Test

```bash
# Bring up interfaces
ip link set eth4 up
ip link set eth5 up
ip link set eth6 up

# Assign IP addresses (example)
ip addr add ************/24 dev eth4
ip addr add ************/24 dev eth5
ip addr add ************/24 dev eth6

# Test connectivity (if connected to network)
ping -I eth4 ***********
ping -I eth5 ***********
ping -I eth6 ***********
```

### Performance Testing

```bash
# Install iperf3 for performance testing
# On server side:
iperf3 -s -B ************

# On client side:
iperf3 -c ************ -t 60 -P 4
```

### Integration Test Suite

Run the provided integration test:
```bash
# Compile and run the test
gcc -o dpaa2_88x5113_test tools/testing/selftests/net/dpaa2_88x5113_integration_test.c
./dpaa2_88x5113_test
```

## Troubleshooting

### Common Issues

1. **PHY Not Detected**
   ```bash
   # Check I2C bus configuration
   ls /sys/bus/i2c/devices/
   
   # Check device tree compilation
   dtc -I dtb -O dts /boot/fsl-lx2160a-rdb.dtb | grep -A 10 88x5113
   ```

2. **No Link Established**
   ```bash
   # Check lane status
   dmesg | grep -i "lane.*lock"
   
   # Check retimer configuration
   dmesg | grep -i "retimer.*configured"
   ```

3. **Speed Issues**
   ```bash
   # Verify interface mode
   ethtool eth4 | grep "Supported link modes"
   
   # Check DPMAC configuration
   cat /sys/class/net/eth4/phy_interface
   ```

### Debug Commands

```bash
# Enable debug messages
echo 8 > /proc/sys/kernel/printk

# Check DPAA2 status
ls-listmac
ls-listni

# Check PHY registers (if accessible)
phytool read eth4/0x1/0x1

# Monitor I2C traffic
i2cdump -y 1 0x77
```

## Migration from Inphi PHY

### Changes Made

1. **Device Tree Updates**:
   - Replaced `inphi_phy` references with `phy_88x5113_*`
   - Updated `phy-connection-type` to `"25gbase-kr"`
   - Added I2C device configuration

2. **Driver Integration**:
   - DPAA2 MAC driver enhanced with 88x5113 support
   - Automatic PHY detection and configuration
   - Link state synchronization

3. **Interface Configuration**:
   - Changed from Inphi-specific to 88x5113 retimer mode
   - Updated speed capabilities to 25G
   - Enhanced lane management

### Compatibility

The integration maintains backward compatibility with:
- Existing DPAA2 Ethernet driver interfaces
- Standard Linux networking tools
- Phylink framework
- Ethtool commands

## Performance Characteristics

### Expected Performance

- **Throughput**: Up to 25 Gbps per interface
- **Latency**: Low latency retimer mode operation
- **Link Recovery**: Automatic lane recovery < 1 second
- **Power Consumption**: Optimized for retimer mode

### Monitoring

```bash
# Monitor interface statistics
watch -n 1 'cat /proc/net/dev | grep eth[456]'

# Monitor PHY status
watch -n 1 'ethtool eth4 | grep -E "(Speed|Link detected)"'
```

## Support and Maintenance

### Log Collection

For support issues, collect the following information:

```bash
# System information
uname -a
cat /proc/version

# Device tree information
dtc -I dtb -O dts /boot/fsl-lx2160a-rdb.dtb > current.dts

# Kernel messages
dmesg > dmesg.log

# Network configuration
ip addr show > network.log
ethtool eth4 > eth4.log
ethtool eth5 > eth5.log
ethtool eth6 > eth6.log

# I2C information
i2cdetect -y 1 > i2c.log
```

### Updates and Patches

Monitor the following for updates:
- Linux kernel networking subsystem
- DPAA2 driver updates
- Marvell PHY driver updates
- Device tree binding updates

This integration provides a robust, high-performance solution for 25G Ethernet connectivity on the LX2160A platform using the Marvell 88x5113 PHY in retimer mode.
