// SPDX-License-Identifier: GPL-2.0
/*
 * Test program for Marvell 88x5113 PHY driver
 *
 * This program tests the basic functionality of the 88x5113 PHY driver
 * including I2C communication, lane status monitoring, and retimer mode
 * configuration.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/ethtool.h>
#include <linux/sockios.h>
#include <net/if.h>
#include <sys/socket.h>

#define TEST_INTERFACE "eth0"  /* Adjust as needed */

struct test_result {
    int passed;
    int failed;
    char description[256];
};

/* Test PHY link status */
static int test_phy_link_status(const char *interface)
{
    int sock;
    struct ifreq ifr;
    struct ethtool_cmd cmd;
    int ret = 0;

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        perror("socket");
        return -1;
    }

    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);

    memset(&cmd, 0, sizeof(cmd));
    cmd.cmd = ETHTOOL_GSET;
    ifr.ifr_data = (char *)&cmd;

    if (ioctl(sock, SIOCETHTOOL, &ifr) < 0) {
        perror("ETHTOOL_GSET");
        ret = -1;
        goto cleanup;
    }

    printf("Interface: %s\n", interface);
    printf("Speed: %u Mbps\n", ethtool_cmd_speed(&cmd));
    printf("Duplex: %s\n", cmd.duplex == DUPLEX_FULL ? "Full" : "Half");
    printf("Link detected: %s\n", cmd.autoneg ? "yes" : "no");

cleanup:
    close(sock);
    return ret;
}

/* Test sysfs attributes */
static int test_sysfs_attributes(const char *interface)
{
    char path[256];
    char buffer[256];
    FILE *file;
    int ret = 0;

    /* Test carrier status */
    snprintf(path, sizeof(path), "/sys/class/net/%s/carrier", interface);
    file = fopen(path, "r");
    if (file) {
        if (fgets(buffer, sizeof(buffer), file)) {
            printf("Carrier: %s", buffer);
        }
        fclose(file);
    } else {
        printf("Warning: Could not read carrier status\n");
        ret = -1;
    }

    /* Test operstate */
    snprintf(path, sizeof(path), "/sys/class/net/%s/operstate", interface);
    file = fopen(path, "r");
    if (file) {
        if (fgets(buffer, sizeof(buffer), file)) {
            printf("Operstate: %s", buffer);
        }
        fclose(file);
    } else {
        printf("Warning: Could not read operstate\n");
        ret = -1;
    }

    /* Test speed */
    snprintf(path, sizeof(path), "/sys/class/net/%s/speed", interface);
    file = fopen(path, "r");
    if (file) {
        if (fgets(buffer, sizeof(buffer), file)) {
            printf("Speed: %s", buffer);
        }
        fclose(file);
    } else {
        printf("Warning: Could not read speed\n");
    }

    return ret;
}

/* Test driver information */
static int test_driver_info(const char *interface)
{
    int sock;
    struct ifreq ifr;
    struct ethtool_drvinfo drvinfo;
    int ret = 0;

    sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        perror("socket");
        return -1;
    }

    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);

    memset(&drvinfo, 0, sizeof(drvinfo));
    drvinfo.cmd = ETHTOOL_GDRVINFO;
    ifr.ifr_data = (char *)&drvinfo;

    if (ioctl(sock, SIOCETHTOOL, &ifr) < 0) {
        perror("ETHTOOL_GDRVINFO");
        ret = -1;
        goto cleanup;
    }

    printf("Driver: %s\n", drvinfo.driver);
    printf("Version: %s\n", drvinfo.version);
    printf("Firmware: %s\n", drvinfo.fw_version);
    printf("Bus info: %s\n", drvinfo.bus_info);

    /* Check if it's our driver */
    if (strstr(drvinfo.driver, "88x5113") || strstr(drvinfo.driver, "marvell")) {
        printf("✓ Marvell 88x5113 driver detected\n");
    } else {
        printf("✗ Expected Marvell 88x5113 driver, got: %s\n", drvinfo.driver);
        ret = -1;
    }

cleanup:
    close(sock);
    return ret;
}

/* Main test function */
int main(int argc, char *argv[])
{
    const char *interface = TEST_INTERFACE;
    struct test_result results[10];
    int test_count = 0;
    int i;

    if (argc > 1) {
        interface = argv[1];
    }

    printf("=== Marvell 88x5113 PHY Driver Test ===\n");
    printf("Testing interface: %s\n\n", interface);

    /* Test 1: Driver Information */
    printf("Test 1: Driver Information\n");
    printf("---------------------------\n");
    results[test_count].passed = (test_driver_info(interface) == 0) ? 1 : 0;
    results[test_count].failed = !results[test_count].passed;
    strcpy(results[test_count].description, "Driver Information");
    test_count++;
    printf("\n");

    /* Test 2: PHY Link Status */
    printf("Test 2: PHY Link Status\n");
    printf("-----------------------\n");
    results[test_count].passed = (test_phy_link_status(interface) == 0) ? 1 : 0;
    results[test_count].failed = !results[test_count].passed;
    strcpy(results[test_count].description, "PHY Link Status");
    test_count++;
    printf("\n");

    /* Test 3: Sysfs Attributes */
    printf("Test 3: Sysfs Attributes\n");
    printf("------------------------\n");
    results[test_count].passed = (test_sysfs_attributes(interface) == 0) ? 1 : 0;
    results[test_count].failed = !results[test_count].passed;
    strcpy(results[test_count].description, "Sysfs Attributes");
    test_count++;
    printf("\n");

    /* Print summary */
    printf("=== Test Summary ===\n");
    int total_passed = 0, total_failed = 0;
    for (i = 0; i < test_count; i++) {
        printf("%s: %s\n", results[i].description, 
               results[i].passed ? "PASSED" : "FAILED");
        total_passed += results[i].passed;
        total_failed += results[i].failed;
    }

    printf("\nTotal: %d passed, %d failed\n", total_passed, total_failed);
    
    if (total_failed == 0) {
        printf("✓ All tests passed!\n");
        return 0;
    } else {
        printf("✗ Some tests failed!\n");
        return 1;
    }
}
