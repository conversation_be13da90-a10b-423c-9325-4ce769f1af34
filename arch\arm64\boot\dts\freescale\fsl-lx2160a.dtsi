// SPDX-License-Identifier: (GPL-2.0 OR MIT)
//
// Device Tree Include file for Layerscape-LX2160A family SoC.
//
// Copyright 2018-2020 NXP

#include <dt-bindings/clock/fsl,qoriq-clockgen.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/thermal/thermal.h>

/memreserve/ 0x80000000 0x00010000;

/ {
	compatible = "fsl,lx2160a";
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		rtc1 = &ftm_alarm0;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		// 8 clusters having 2 Cortex-A72 cores each
		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x0>;
			clocks = <&clockgen QORIQ_CLK_CMUX 0>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster0_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x1>;
			clocks = <&clockgen QORIQ_CLK_CMUX 0>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster0_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu100: cpu@100 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x100>;
			clocks = <&clockgen QORIQ_CLK_CMUX 1>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster1_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu101: cpu@101 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x101>;
			clocks = <&clockgen QORIQ_CLK_CMUX 1>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster1_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu200: cpu@200 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x200>;
			clocks = <&clockgen QORIQ_CLK_CMUX 2>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster2_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu201: cpu@201 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x201>;
			clocks = <&clockgen QORIQ_CLK_CMUX 2>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster2_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu300: cpu@300 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x300>;
			clocks = <&clockgen QORIQ_CLK_CMUX 3>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster3_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu301: cpu@301 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x301>;
			clocks = <&clockgen QORIQ_CLK_CMUX 3>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster3_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu400: cpu@400 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x400>;
			clocks = <&clockgen QORIQ_CLK_CMUX 4>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster4_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu401: cpu@401 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x401>;
			clocks = <&clockgen QORIQ_CLK_CMUX 4>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster4_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu500: cpu@500 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x500>;
			clocks = <&clockgen QORIQ_CLK_CMUX 5>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster5_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu501: cpu@501 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x501>;
			clocks = <&clockgen QORIQ_CLK_CMUX 5>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster5_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu600: cpu@600 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x600>;
			clocks = <&clockgen QORIQ_CLK_CMUX 6>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster6_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu601: cpu@601 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x601>;
			clocks = <&clockgen QORIQ_CLK_CMUX 6>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster6_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu700: cpu@700 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x700>;
			clocks = <&clockgen QORIQ_CLK_CMUX 7>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster7_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cpu701: cpu@701 {
			device_type = "cpu";
			compatible = "arm,cortex-a72";
			enable-method = "psci";
			reg = <0x701>;
			clocks = <&clockgen QORIQ_CLK_CMUX 7>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			i-cache-size = <0xC000>;
			i-cache-line-size = <64>;
			i-cache-sets = <192>;
			next-level-cache = <&cluster7_l2>;
			cpu-idle-states = <&cpu_pw15>;
			#cooling-cells = <2>;
		};

		cluster0_l2: l2-cache0 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cluster1_l2: l2-cache1 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cluster2_l2: l2-cache2 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cluster3_l2: l2-cache3 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cluster4_l2: l2-cache4 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cluster5_l2: l2-cache5 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cluster6_l2: l2-cache6 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cluster7_l2: l2-cache7 {
			compatible = "cache";
			cache-size = <0x100000>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			cache-level = <2>;
		};

		cpu_pw15: cpu-pw15 {
			compatible = "arm,idle-state";
			idle-state-name = "PW15";
			arm,psci-suspend-param = <0x0>;
			entry-latency-us = <2000>;
			exit-latency-us = <2000>;
			min-residency-us = <6000>;
		  };
	};

	gic: interrupt-controller@6000000 {
		compatible = "arm,gic-v3";
		reg = <0x0 0x06000000 0 0x10000>, // GIC Dist
			<0x0 0x06200000 0 0x200000>, // GICR (RD_base +
						     // SGI_base)
			<0x0 0x0c0c0000 0 0x2000>, // GICC
			<0x0 0x0c0d0000 0 0x1000>, // GICH
			<0x0 0x0c0e0000 0 0x20000>; // GICV
		#interrupt-cells = <3>;
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		interrupt-controller;
		interrupts = <GIC_PPI 9 IRQ_TYPE_LEVEL_HIGH>;

		its: gic-its@6020000 {
			compatible = "arm,gic-v3-its";
			msi-controller;
			reg = <0x0 0x6020000 0 0x20000>;
		};
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 14 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 11 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_PPI 10 IRQ_TYPE_LEVEL_HIGH>;
	};

	pmu {
		compatible = "arm,cortex-a72-pmu";
		interrupts = <GIC_PPI 7 IRQ_TYPE_LEVEL_LOW>;
	};

	psci {
		compatible = "arm,psci-0.2";
		method = "smc";
	};

	memory@80000000 {
		// DRAM space - 1, size : 2 GB DRAM
		device_type = "memory";
		reg = <0x00000000 0x80000000 0 0x80000000>;
	};

	ddr1: memory-controller@1080000 {
		compatible = "fsl,qoriq-memory-controller";
		reg = <0x0 0x1080000 0x0 0x1000>;
		interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
		little-endian;
	};

	ddr2: memory-controller@1090000 {
		compatible = "fsl,qoriq-memory-controller";
		reg = <0x0 0x1090000 0x0 0x1000>;
		interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
		little-endian;
	};

	// One clock unit-sysclk node which bootloader require during DT fix-up
	sysclk: sysclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <100000000>; // fixed up by bootloader
		clock-output-names = "sysclk";
	};

	thermal-zones {
		cluster6-7 {
			polling-delay-passive = <1000>;
			polling-delay = <5000>;
			thermal-sensors = <&tmu 0>;

			trips {
				cluster6_7_alert: cluster6-7-alert {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cluster6_7_crit: cluster6-7-crit {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&cluster6_7_alert>;
					cooling-device =
						<&cpu0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu100 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu101 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu200 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu201 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu300 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu301 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu400 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu401 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu500 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu501 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu600 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu601 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu700 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&cpu701 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};

		ddr-cluster5 {
			polling-delay-passive = <1000>;
			polling-delay = <5000>;
			thermal-sensors = <&tmu 1>;

			trips {
				ddr-cluster5-alert {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				ddr-cluster5-crit {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		wriop {
			polling-delay-passive = <1000>;
			polling-delay = <5000>;
			thermal-sensors = <&tmu 2>;

			trips {
				wriop-alert {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				wriop-crit {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		dce-qbman-hsio2 {
			polling-delay-passive = <1000>;
			polling-delay = <5000>;
			thermal-sensors = <&tmu 3>;

			trips {
				dce-qbman-alert {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				dce-qbman-crit {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		ccn-dpaa-tbu {
			polling-delay-passive = <1000>;
			polling-delay = <5000>;
			thermal-sensors = <&tmu 4>;

			trips {
				ccn-dpaa-alert {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				ccn-dpaa-crit {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		cluster4-hsio3 {
			polling-delay-passive = <1000>;
			polling-delay = <5000>;
			thermal-sensors = <&tmu 5>;

			trips {
				clust4-hsio3-alert {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				clust4-hsio3-crit {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		cluster2-3 {
			polling-delay-passive = <1000>;
			polling-delay = <5000>;
			thermal-sensors = <&tmu 6>;

			trips {
				cluster2-3-alert {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cluster2-3-crit {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		dma-ranges = <0x0 0x0 0x0 0x0 0x10000 0x00000000>;

		serdes_1: phy@1ea0000 {
			compatible = "fsl,lynx-28g";
			reg = <0x0 0x1ea0000 0x0 0x1e30>;
			#phy-cells = <1>;
		};

		crypto: crypto@8000000 {
			compatible = "fsl,sec-v5.0", "fsl,sec-v4.0";
			fsl,sec-era = <10>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x00 0x8000000 0x100000>;
			reg = <0x00 0x8000000 0x0 0x100000>;
			interrupts = <GIC_SPI 139 IRQ_TYPE_LEVEL_HIGH>;
			dma-coherent;
			status = "disabled";

			sec_jr0: jr@10000 {
				compatible = "fsl,sec-v5.0-job-ring",
					     "fsl,sec-v4.0-job-ring";
				reg        = <0x10000 0x10000>;
				interrupts = <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>;
			};

			sec_jr1: jr@20000 {
				compatible = "fsl,sec-v5.0-job-ring",
					     "fsl,sec-v4.0-job-ring";
				reg        = <0x20000 0x10000>;
				interrupts = <GIC_SPI 141 IRQ_TYPE_LEVEL_HIGH>;
			};

			sec_jr2: jr@30000 {
				compatible = "fsl,sec-v5.0-job-ring",
					     "fsl,sec-v4.0-job-ring";
				reg        = <0x30000 0x10000>;
				interrupts = <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>;
			};

			sec_jr3: jr@40000 {
				compatible = "fsl,sec-v5.0-job-ring",
					     "fsl,sec-v4.0-job-ring";
				reg        = <0x40000 0x10000>;
				interrupts = <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>;
			};
		};

		clockgen: clock-controller@1300000 {
			compatible = "fsl,lx2160a-clockgen";
			reg = <0 0x1300000 0 0xa0000>;
			#clock-cells = <2>;
			clocks = <&sysclk>;
		};

		dcfg: syscon@1e00000 {
			compatible = "fsl,lx2160a-dcfg", "syscon";
			reg = <0x0 0x1e00000 0x0 0x10000>;
			little-endian;
		};

		isc: syscon@1f70000 {
			compatible = "fsl,lx2160a-isc", "syscon";
			reg = <0x0 0x1f70000 0x0 0x10000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x0 0x0 0x1f70000 0x10000>;

			extirq: interrupt-controller@14 {
				compatible = "fsl,lx2160a-extirq", "fsl,ls1088a-extirq";
				#interrupt-cells = <2>;
				#address-cells = <0>;
				interrupt-controller;
				reg = <0x14 4>;
				interrupt-map =
					<0 0 &gic GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
					<1 0 &gic GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
					<2 0 &gic GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
					<3 0 &gic GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
					<4 0 &gic GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
					<5 0 &gic GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
					<6 0 &gic GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
					<7 0 &gic GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
					<8 0 &gic GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
					<9 0 &gic GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
					<10 0 &gic GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
					<11 0 &gic GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-map-mask = <0xffffffff 0x0>;
			};
		};

		tmu: tmu@1f80000 {
			compatible = "fsl,qoriq-tmu";
			reg = <0x0 0x1f80000 0x0 0x10000>;
			interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
			fsl,tmu-range = <0x800000e6 0x8001017d>;
			fsl,tmu-calibration =
				/* Calibration data group 1 */
				<0x00000000 0x00000035
				/* Calibration data group 2 */
				0x00000001 0x00000154>;
			little-endian;
			#thermal-sensor-cells = <1>;
		};

		i2c0: i2c@2000000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2000000 0x0 0x10000>;
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			scl-gpios = <&gpio2 15 GPIO_ACTIVE_HIGH>;
			status = "disabled";
		};

		i2c1: i2c@2010000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2010000 0x0 0x10000>;
			interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			status = "disabled";
		};

		i2c2: i2c@2020000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2020000 0x0 0x10000>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			status = "disabled";
		};

		i2c3: i2c@2030000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2030000 0x0 0x10000>;
			interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			status = "disabled";
		};

		i2c4: i2c@2040000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2040000 0x0 0x10000>;
			interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			scl-gpios = <&gpio2 16 GPIO_ACTIVE_HIGH>;
			status = "disabled";
		};

		i2c5: i2c@2050000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2050000 0x0 0x10000>;
			interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			status = "disabled";
		};

		i2c6: i2c@2060000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2060000 0x0 0x10000>;
			interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			status = "disabled";
		};

		i2c7: i2c@2070000 {
			compatible = "fsl,vf610-i2c";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2070000 0x0 0x10000>;
			interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
			clock-names = "i2c";
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(16)>;
			status = "disabled";
		};

		fspi: spi@20c0000 {
			compatible = "nxp,lx2160a-fspi";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x20c0000 0x0 0x10000>,
			      <0x0 0x20000000 0x0 0x10000000>;
			reg-names = "fspi_base", "fspi_mmap";
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(4)>,
				 <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(4)>;
			clock-names = "fspi_en", "fspi";
			status = "disabled";
		};

		dspi0: spi@2100000 {
			compatible = "fsl,lx2160a-dspi", "fsl,ls2085a-dspi";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2100000 0x0 0x10000>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(8)>;
			clock-names = "dspi";
			spi-num-chipselects = <5>;
			bus-num = <0>;
			status = "disabled";
		};

		dspi1: spi@2110000 {
			compatible = "fsl,lx2160a-dspi", "fsl,ls2085a-dspi";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2110000 0x0 0x10000>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(8)>;
			clock-names = "dspi";
			spi-num-chipselects = <5>;
			bus-num = <1>;
			status = "disabled";
		};

		dspi2: spi@2120000 {
			compatible = "fsl,lx2160a-dspi", "fsl,ls2085a-dspi";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x2120000 0x0 0x10000>;
			interrupts = <GIC_SPI 241 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(8)>;
			clock-names = "dspi";
			spi-num-chipselects = <5>;
			bus-num = <2>;
			status = "disabled";
		};

		esdhc0: esdhc@2140000 {
			compatible = "fsl,esdhc";
			reg = <0x0 0x2140000 0x0 0x10000>;
			interrupts = <0 28 0x4>; /* Level high type */
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(2)>;
			dma-coherent;
			voltage-ranges = <1800 1800 3300 3300>;
			sdhci,auto-cmd12;
			little-endian;
			bus-width = <4>;
			status = "disabled";
		};

		esdhc1: esdhc@2150000 {
			compatible = "fsl,esdhc";
			reg = <0x0 0x2150000 0x0 0x10000>;
			interrupts = <0 63 0x4>; /* Level high type */
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(2)>;
			dma-coherent;
			voltage-ranges = <1800 1800 3300 3300>;
			sdhci,auto-cmd12;
			broken-cd;
			little-endian;
			bus-width = <4>;
			status = "disabled";
		};

		can0: can@2180000 {
			compatible = "fsl,lx2160ar1-flexcan";
			reg = <0x0 0x2180000 0x0 0x10000>;
			interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(8)>,
				 <&clockgen QORIQ_CLK_SYSCLK 0>;
			clock-names = "ipg", "per";
			fsl,clk-source = <0>;
			status = "disabled";
		};

		can1: can@2190000 {
			compatible = "fsl,lx2160ar1-flexcan";
			reg = <0x0 0x2190000 0x0 0x10000>;
			interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(8)>,
				 <&clockgen QORIQ_CLK_SYSCLK 0>;
			clock-names = "ipg", "per";
			fsl,clk-source = <0>;
			status = "disabled";
		};

		uart0: serial@21c0000 {
			compatible = "arm,sbsa-uart","arm,pl011";
			reg = <0x0 0x21c0000 0x0 0x1000>;
			interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
			current-speed = <115200>;
			status = "disabled";
		};

		uart1: serial@21d0000 {
			compatible = "arm,sbsa-uart","arm,pl011";
			reg = <0x0 0x21d0000 0x0 0x1000>;
			interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
			current-speed = <115200>;
			status = "disabled";
		};

		uart2: serial@21e0000 {
			compatible = "arm,sbsa-uart","arm,pl011";
			reg = <0x0 0x21e0000 0x0 0x1000>;
			interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
			current-speed = <115200>;
			status = "disabled";
		};

		uart3: serial@21f0000 {
			compatible = "arm,sbsa-uart","arm,pl011";
			reg = <0x0 0x21f0000 0x0 0x1000>;
			interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
			current-speed = <115200>;
			status = "disabled";
		};

		gpio0: gpio@2300000 {
			compatible = "fsl,qoriq-gpio";
			reg = <0x0 0x2300000 0x0 0x10000>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			gpio-controller;
			little-endian;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio1: gpio@2310000 {
			compatible = "fsl,qoriq-gpio";
			reg = <0x0 0x2310000 0x0 0x10000>;
			interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
			gpio-controller;
			little-endian;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio2: gpio@2320000 {
			compatible = "fsl,qoriq-gpio";
			reg = <0x0 0x2320000 0x0 0x10000>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			gpio-controller;
			little-endian;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio3: gpio@2330000 {
			compatible = "fsl,qoriq-gpio";
			reg = <0x0 0x2330000 0x0 0x10000>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			gpio-controller;
			little-endian;
			#gpio-cells = <2>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		watchdog@23a0000 {
			compatible = "arm,sbsa-gwdt";
			reg = <0x0 0x23a0000 0 0x1000>,
			      <0x0 0x2390000 0 0x1000>;
			interrupts = <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>;
			timeout-sec = <30>;
		};

		rcpm: power-controller@1e34040 {
			compatible = "fsl,lx2160a-rcpm", "fsl,qoriq-rcpm-2.1+";
			reg = <0x0 0x1e34040 0x0 0x1c>;
			#fsl,rcpm-wakeup-cells = <7>;
			little-endian;
		};

		ftm_alarm0: timer@2800000 {
			compatible = "fsl,lx2160a-ftm-alarm";
			reg = <0x0 0x2800000 0x0 0x10000>;
			fsl,rcpm-wakeup = <&rcpm 0x0 0x0 0x0 0x0 0x4000 0x0 0x0>;
			interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
		};

		usb0: usb@3100000 {
			compatible = "fsl,lx2160a-dwc3", "snps,dwc3";
			reg = <0x0 0x3100000 0x0 0x10000>;
			interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
			dr_mode = "host";
			snps,quirk-frame-length-adjustment = <0x20>;
			usb3-lpm-capable;
			snps,dis_rxdet_inp3_quirk;
			snps,incr-burst-type-adjustment = <1>, <4>, <8>, <16>;
			snps,host-vbus-glitches;
			dma-coherent;
			status = "disabled";
		};

		usb1: usb@3110000 {
			compatible = "fsl,lx2160a-dwc3", "snps,dwc3";
			reg = <0x0 0x3110000 0x0 0x10000>;
			interrupts = <GIC_SPI 81 IRQ_TYPE_LEVEL_HIGH>;
			dr_mode = "host";
			snps,quirk-frame-length-adjustment = <0x20>;
			usb3-lpm-capable;
			snps,dis_rxdet_inp3_quirk;
			snps,incr-burst-type-adjustment = <1>, <4>, <8>, <16>;
			snps,host-vbus-glitches;
			dma-coherent;
			status = "disabled";
		};

		sata0: sata@3200000 {
			compatible = "fsl,lx2160a-ahci";
			reg = <0x0 0x3200000 0x0 0x10000>,
			      <0x7 0x100520 0x0 0x4>;
			reg-names = "ahci", "sata-ecc";
			interrupts = <GIC_SPI 133 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(4)>;
			dma-coherent;
			status = "disabled";
		};

		sata1: sata@3210000 {
			compatible = "fsl,lx2160a-ahci";
			reg = <0x0 0x3210000 0x0 0x10000>,
			      <0x7 0x100520 0x0 0x4>;
			reg-names = "ahci", "sata-ecc";
			interrupts = <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(4)>;
			dma-coherent;
			status = "disabled";
		};

		sata2: sata@3220000 {
			compatible = "fsl,lx2160a-ahci";
			reg = <0x0 0x3220000 0x0 0x10000>,
			      <0x7 0x100520 0x0 0x4>;
			reg-names = "ahci", "sata-ecc";
			interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(4)>;
			dma-coherent;
			status = "disabled";
		};

		sata3: sata@3230000 {
			compatible = "fsl,lx2160a-ahci";
			reg = <0x0 0x3230000 0x0 0x10000>,
			      <0x7 0x100520 0x0 0x4>;
			reg-names = "ahci", "sata-ecc";
			interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(4)>;
			dma-coherent;
			status = "disabled";
		};

		pcie1: pcie@3400000 {
			compatible = "fsl,ls2088a-pcie";
			reg = <0x00 0x03400000 0x0 0x00100000   /* controller registers */
			       0x80 0x00000000 0x0 0x00002000>; /* configuration space */
			reg-names = "regs", "config";
			interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>, /* AER interrupt */
				     <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>, /* PME interrupt */
				     <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>; /* controller interrupt */
			interrupt-names = "aer", "pme", "intr";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			dma-coherent;
			num-viewport = <8>;
			bus-range = <0x0 0xff>;
			ranges = <0x81000000 0x0 0x00000000 0x80 0x00010000 0x0 0x00010000
				  0x82000000 0x0 0x40000000 0x80 0x40000000 0x0 0x40000000>; /* non-prefetchable memory */
			msi-parent = <&its>;
			iommu-map = <0 &smmu 0 1>; /* This is fixed-up by u-boot */
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 7>;
			interrupt-map = <0000 0 0 1 &gic 0 0 GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 2 &gic 0 0 GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 3 &gic 0 0 GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 4 &gic 0 0 GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		pcie_ep1: pcie_ep@3400000 {
			compatible = "fsl,lx2160ar2-pcie-ep", "fsl,ls-pcie-ep";
			reg = <0x00 0x03400000 0x0 0x00100000
			       0x80 0x00000000 0x8 0x00000000>;
			reg-names = "regs", "addr_space";
			num-ob-windows = <8>;
			num-ib-windows = <8>;
			status = "disabled";
		};

		pcie2: pcie@3500000 {
			compatible = "fsl,ls2088a-pcie";
			reg = <0x00 0x03500000 0x0 0x00100000   /* controller registers */
			       0x88 0x00000000 0x0 0x00002000>; /* configuration space */
			reg-names = "regs", "config";
			interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>, /* AER interrupt */
				     <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>, /* PME interrupt */
				     <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>; /* controller interrupt */
			interrupt-names = "aer", "pme", "intr";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			dma-coherent;
			num-viewport = <8>;
			bus-range = <0x0 0xff>;
			ranges = <0x81000000 0x0 0x00000000 0x88 0x00010000 0x0 0x00010000
				  0x82000000 0x0 0x40000000 0x88 0x40000000 0x0 0x40000000>; /* non-prefetchable memory */
			msi-parent = <&its>;
			iommu-map = <0 &smmu 0 1>; /* This is fixed-up by u-boot */
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 7>;
			interrupt-map = <0000 0 0 1 &gic 0 0 GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 2 &gic 0 0 GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 3 &gic 0 0 GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 4 &gic 0 0 GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		pcie_ep2: pcie_ep@3500000 {
			compatible = "fsl,lx2160ar2-pcie-ep", "fsl,ls-pcie-ep";
			reg = <0x00 0x03500000 0x0 0x00100000
			       0x88 0x00000000 0x8 0x00000000>;
			reg-names = "regs", "addr_space";
			num-ob-windows = <8>;
			num-ib-windows = <8>;
			status = "disabled";
		};

		pcie3: pcie@3600000 {
			compatible = "fsl,ls2088a-pcie";
			reg = <0x00 0x03600000 0x0 0x00100000   /* controller registers */
			       0x90 0x00000000 0x0 0x00002000>; /* configuration space */
			reg-names = "regs", "config";
			interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>, /* AER interrupt */
				     <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>, /* PME interrupt */
				     <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>; /* controller interrupt */
			interrupt-names = "aer", "pme", "intr";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			dma-coherent;
			num-viewport = <256>;
			bus-range = <0x0 0xff>;
			ranges = <0x81000000 0x0 0x00000000 0x90 0x00010000 0x0 0x00010000
				  0x82000000 0x0 0x40000000 0x90 0x40000000 0x0 0x40000000>; /* non-prefetchable memory */
			msi-parent = <&its>;
			iommu-map = <0 &smmu 0 1>; /* This is fixed-up by u-boot */
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 7>;
			interrupt-map = <0000 0 0 1 &gic 0 0 GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 2 &gic 0 0 GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 3 &gic 0 0 GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 4 &gic 0 0 GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		pcie_ep3: pcie_ep@3600000 {
			compatible = "fsl,lx2160ar2-pcie-ep", "fsl,ls-pcie-ep";
			reg = <0x00 0x03600000 0x0 0x00100000
			       0x90 0x00000000 0x8 0x00000000>;
			reg-names = "regs", "addr_space";
			num-ob-windows = <256>;
			num-ib-windows = <24>;
			status = "disabled";
		};

		pcie4: pcie@3700000 {
			compatible = "fsl,ls2088a-pcie";
			reg = <0x00 0x03700000 0x0 0x00100000   /* controller registers */
			       0x98 0x00000000 0x0 0x00002000>; /* configuration space */
			reg-names = "regs", "config";
			interrupts = <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>, /* AER interrupt */
				     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>, /* PME interrupt */
				     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>; /* controller interrupt */
			interrupt-names = "aer", "pme", "intr";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			dma-coherent;
			num-viewport = <8>;
			bus-range = <0x0 0xff>;
			ranges = <0x81000000 0x0 0x00000000 0x98 0x00010000 0x0 0x00010000
				  0x82000000 0x0 0x40000000 0x98 0x40000000 0x0 0x40000000>; /* non-prefetchable memory */
			msi-parent = <&its>;
			iommu-map = <0 &smmu 0 1>; /* This is fixed-up by u-boot */
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 7>;
			interrupt-map = <0000 0 0 1 &gic 0 0 GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 2 &gic 0 0 GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 3 &gic 0 0 GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 4 &gic 0 0 GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		pcie_ep4: pcie_ep@3700000 {
			compatible = "fsl,lx2160ar2-pcie-ep", "fsl,ls-pcie-ep";
			reg = <0x00 0x03700000 0x0 0x00100000
			       0x98 0x00000000 0x8 0x00000000>;
			reg-names = "regs", "addr_space";
			num-ob-windows = <8>;
			num-ib-windows = <8>;
			status = "disabled";
		};

		pcie5: pcie@3800000 {
			compatible = "fsl,ls2088a-pcie";
			reg = <0x00 0x03800000 0x0 0x00100000   /* controller registers */
			       0xa0 0x00000000 0x0 0x00002000>; /* configuration space */
			reg-names = "regs", "config";
			interrupts = <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>, /* AER interrupt */
				     <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>, /* PME interrupt */
				     <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>; /* controller interrupt */
			interrupt-names = "aer", "pme", "intr";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			dma-coherent;
			num-viewport = <256>;
			bus-range = <0x0 0xff>;
			ranges = <0x81000000 0x0 0x00000000 0xa0 0x00010000 0x0 0x00010000
				  0x82000000 0x0 0x40000000 0xa0 0x40000000 0x0 0x40000000>; /* non-prefetchable memory */
			msi-parent = <&its>;
			iommu-map = <0 &smmu 0 1>; /* This is fixed-up by u-boot */
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 7>;
			interrupt-map = <0000 0 0 1 &gic 0 0 GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 2 &gic 0 0 GIC_SPI 130 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 3 &gic 0 0 GIC_SPI 131 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 4 &gic 0 0 GIC_SPI 132 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		pcie_ep5: pcie_ep@3800000 {
			compatible = "fsl,lx2160ar2-pcie-ep", "fsl,ls-pcie-ep";
			reg = <0x00 0x03800000 0x0 0x00100000
			       0xa0 0x00000000 0x8 0x00000000>;
			reg-names = "regs", "addr_space";
			num-ob-windows = <256>;
			num-ib-windows = <24>;
			status = "disabled";
		};

		pcie6: pcie@3900000 {
			compatible = "fsl,ls2088a-pcie";
			reg = <0x00 0x03900000 0x0 0x00100000   /* controller registers */
			       0xa8 0x00000000 0x0 0x00002000>; /* configuration space */
			reg-names = "regs", "config";
			interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>, /* AER interrupt */
				     <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>, /* PME interrupt */
				     <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>; /* controller interrupt */
			interrupt-names = "aer", "pme", "intr";
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			dma-coherent;
			num-viewport = <8>;
			bus-range = <0x0 0xff>;
			ranges = <0x81000000 0x0 0x00000000 0xa8 0x00010000 0x0 0x00010000
				  0x82000000 0x0 0x40000000 0xa8 0x40000000 0x0 0x40000000>; /* non-prefetchable memory */
			msi-parent = <&its>;
			iommu-map = <0 &smmu 0 1>; /* This is fixed-up by u-boot */
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 7>;
			interrupt-map = <0000 0 0 1 &gic 0 0 GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 2 &gic 0 0 GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 3 &gic 0 0 GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>,
					<0000 0 0 4 &gic 0 0 GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>;
			status = "disabled";
		};

		pcie_ep6: pcie_ep@3900000 {
			compatible = "fsl,lx2160ar2-pcie-ep", "fsl,ls-pcie-ep";
			reg = <0x00 0x03900000 0x0 0x00100000
			       0xa8 0x00000000 0x8 0x00000000>;
			reg-names = "regs", "addr_space";
			num-ob-windows = <8>;
			num-ib-windows = <8>;
			status = "disabled";
		};

		smmu: iommu@5000000 {
			compatible = "arm,mmu-500";
			reg = <0 0x5000000 0 0x800000>;
			#iommu-cells = <1>;
			#global-interrupts = <14>;
				     // global secure fault
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
				     // combined secure
				     <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
				     // global non-secure fault
				     <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
				     // combined non-secure
				     <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>,
				     // performance counter interrupts 0-9
				     <GIC_SPI 211 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 212 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 213 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 214 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 215 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 216 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 217 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 218 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 219 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 220 IRQ_TYPE_LEVEL_HIGH>,
				     // per context interrupt, 64 interrupts
				     <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 147 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 149 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 150 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 152 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 155 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 158 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 159 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 160 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 161 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 162 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 164 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 165 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 166 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 167 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 168 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 169 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 170 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 171 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 172 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 173 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 176 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 178 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 179 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 180 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 181 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 182 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 183 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 185 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 186 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 187 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 189 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 190 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 191 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 192 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 193 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 194 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 195 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 196 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 197 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 198 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 199 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 200 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 201 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 202 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 203 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 204 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 205 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 206 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 207 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 208 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 209 IRQ_TYPE_LEVEL_HIGH>;
			dma-coherent;
		};

		console@8340020 {
			compatible = "fsl,dpaa2-console";
			reg = <0x00000000 0x08340020 0 0x2>;
		};

		ptp-timer@8b95000 {
			compatible = "fsl,dpaa2-ptp";
			reg = <0x0 0x8b95000 0x0 0x100>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(2)>;
			little-endian;
			fsl,extts-fifo;
		};

		/* WRIOP0: 0x8b8_0000, E-MDIO1: 0x1_6000 */
		emdio1: mdio@8b96000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8b96000 0x0 0x1000>;
			interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			little-endian;
			clock-frequency = <2500000>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(2)>;
			status = "disabled";
		};

		emdio2: mdio@8b97000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8b97000 0x0 0x1000>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			clock-frequency = <2500000>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(2)>;
			status = "disabled";
		};

		/* MDIO bus for 88x5113 PHY - uses I2C for actual communication */
		emdio_88x5113: mdio@8b98000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8b98000 0x0 0x1000>;
			interrupts = <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			clock-frequency = <2500000>;
			clocks = <&clockgen QORIQ_CLK_PLATFORM_PLL
					    QORIQ_CLK_PLL_DIV(2)>;
			status = "disabled";
		};

		pcs_mdio1: mdio@8c07000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c07000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs1: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio2: mdio@8c0b000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c0b000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs2: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio3: mdio@8c0f000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c0f000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs3: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio4: mdio@8c13000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c13000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs4: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio5: mdio@8c17000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c17000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs5: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio6: mdio@8c1b000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c1b000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs6: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio7: mdio@8c1f000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c1f000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs7: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio8: mdio@8c23000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c23000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs8: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio9: mdio@8c27000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c27000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs9: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio10: mdio@8c2b000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c2b000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs10: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio11: mdio@8c2f000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c2f000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs11: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio12: mdio@8c33000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c33000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs12: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio13: mdio@8c37000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c37000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs13: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio14: mdio@8c3b000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c3b000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs14: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio15: mdio@8c3f000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c3f000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs15: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio16: mdio@8c43000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c43000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs16: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio17: mdio@8c47000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c47000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs17: ethernet-phy@0 {
				reg = <0>;
			};
		};

		pcs_mdio18: mdio@8c4b000 {
			compatible = "fsl,fman-memac-mdio";
			reg = <0x0 0x8c4b000 0x0 0x1000>;
			little-endian;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "disabled";

			pcs18: ethernet-phy@0 {
				reg = <0>;
			};
		};

		fsl_mc: fsl-mc@80c000000 {
			compatible = "fsl,qoriq-mc";
			reg = <0x00000008 0x0c000000 0 0x40>,
			      <0x00000000 0x08340000 0 0x40000>;
			msi-parent = <&its>;
			/* iommu-map property is fixed up by u-boot */
			iommu-map = <0 &smmu 0 0>;
			dma-coherent;
			#address-cells = <3>;
			#size-cells = <1>;

			/*
			 * Region type 0x0 - MC portals
			 * Region type 0x1 - QBMAN portals
			 */
			ranges = <0x0 0x0 0x0 0x8 0x0c000000 0x4000000
				  0x1 0x0 0x0 0x8 0x18000000 0x8000000>;

			/*
			 * Define the maximum number of MACs present on the SoC.
			 */
			dpmacs {
				#address-cells = <1>;
				#size-cells = <0>;

				dpmac1: ethernet@1 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x1>;
					pcs-handle = <&pcs1>;
				};

				dpmac2: ethernet@2 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x2>;
					pcs-handle = <&pcs2>;
				};

				dpmac3: ethernet@3 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x3>;
					pcs-handle = <&pcs3>;
				};

				dpmac4: ethernet@4 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x4>;
					pcs-handle = <&pcs4>;
				};

				dpmac5: ethernet@5 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x5>;
					pcs-handle = <&pcs5>;
				};

				dpmac6: ethernet@6 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x6>;
					pcs-handle = <&pcs6>;
				};

				dpmac7: ethernet@7 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x7>;
					pcs-handle = <&pcs7>;
				};

				dpmac8: ethernet@8 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x8>;
					pcs-handle = <&pcs8>;
				};

				dpmac9: ethernet@9 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x9>;
					pcs-handle = <&pcs9>;
				};

				dpmac10: ethernet@a {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0xa>;
					pcs-handle = <&pcs10>;
				};

				dpmac11: ethernet@b {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0xb>;
					pcs-handle = <&pcs11>;
				};

				dpmac12: ethernet@c {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0xc>;
					pcs-handle = <&pcs12>;
				};

				dpmac13: ethernet@d {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0xd>;
					pcs-handle = <&pcs13>;
				};

				dpmac14: ethernet@e {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0xe>;
					pcs-handle = <&pcs14>;
				};

				dpmac15: ethernet@f {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0xf>;
					pcs-handle = <&pcs15>;
				};

				dpmac16: ethernet@10 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x10>;
					pcs-handle = <&pcs16>;
				};

				dpmac17: ethernet@11 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x11>;
					pcs-handle = <&pcs17>;
				};

				dpmac18: ethernet@12 {
					compatible = "fsl,qoriq-mc-dpmac";
					reg = <0x12>;
					pcs-handle = <&pcs18>;
				};
			};
		};
	};

	firmware {
		optee: optee {
			compatible = "linaro,optee-tz";
			method = "smc";
			status = "disabled";
		};
	};
};
