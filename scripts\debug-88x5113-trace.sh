#!/bin/bash
# SPDX-License-Identifier: GPL-2.0
#
# Debug trace script for 88x5113 PHY and DPAA2 Ethernet integration
#
# This script monitors and displays all debug messages from the MAC driver
# to PHY driver communication process in real-time.

set -e

SCRIPT_NAME="88x5113 Debug Trace Monitor"
LOG_FILE="/tmp/88x5113_debug.log"
FILTER_FILE="/tmp/88x5113_filter.log"

echo "========================================"
echo "$SCRIPT_NAME"
echo "========================================"
echo "Monitoring MAC to PHY driver communication"
echo "Log file: $LOG_FILE"
echo "Press Ctrl+C to stop monitoring"
echo

# Function to display colored output
print_colored() {
    local color=$1
    local message=$2
    case $color in
        "red")    echo -e "\033[31m$message\033[0m" ;;
        "green")  echo -e "\033[32m$message\033[0m" ;;
        "yellow") echo -e "\033[33m$message\033[0m" ;;
        "blue")   echo -e "\033[34m$message\033[0m" ;;
        "purple") echo -e "\033[35m$message\033[0m" ;;
        "cyan")   echo -e "\033[36m$message\033[0m" ;;
        *)        echo "$message" ;;
    esac
}

# Function to parse and display debug messages
parse_debug_message() {
    local line="$1"
    local timestamp=$(echo "$line" | grep -o '^\[[^]]*\]' || echo "[$(date '+%H:%M:%S')]")
    local message=$(echo "$line" | sed 's/^[^]]*\] *//')
    
    if [[ "$message" =~ \[DPAA2-ETH\] ]]; then
        if [[ "$message" =~ "Step " ]]; then
            print_colored "blue" "$timestamp MAC: $message"
        elif [[ "$message" =~ "====" ]]; then
            print_colored "cyan" "$timestamp MAC: $message"
        elif [[ "$message" =~ "✓" ]]; then
            print_colored "green" "$timestamp MAC: $message"
        elif [[ "$message" =~ "✗" ]]; then
            print_colored "red" "$timestamp MAC: $message"
        else
            print_colored "blue" "$timestamp MAC: $message"
        fi
    elif [[ "$message" =~ \[88x5113-PHY\] ]]; then
        if [[ "$message" =~ "I2C " ]]; then
            print_colored "yellow" "$timestamp PHY: $message"
        elif [[ "$message" =~ "====" ]]; then
            print_colored "purple" "$timestamp PHY: $message"
        elif [[ "$message" =~ "✓" ]]; then
            print_colored "green" "$timestamp PHY: $message"
        elif [[ "$message" =~ "✗" ]]; then
            print_colored "red" "$timestamp PHY: $message"
        else
            print_colored "purple" "$timestamp PHY: $message"
        fi
    else
        echo "$timestamp $message"
    fi
}

# Function to monitor kernel messages
monitor_kernel_messages() {
    echo "Starting kernel message monitoring..."
    echo "Looking for DPAA2-ETH and 88x5113-PHY messages..."
    echo
    
    # Clear previous log
    > "$LOG_FILE"
    
    # Monitor dmesg in real-time
    dmesg -w | grep -E "\[DPAA2-ETH\]|\[88x5113-PHY\]" | while read -r line; do
        echo "$line" >> "$LOG_FILE"
        parse_debug_message "$line"
    done
}

# Function to show recent messages
show_recent_messages() {
    echo "Recent 88x5113 and DPAA2 messages:"
    echo "=================================="
    
    dmesg | grep -E "\[DPAA2-ETH\]|\[88x5113-PHY\]" | tail -50 | while read -r line; do
        parse_debug_message "$line"
    done
    echo
}

# Function to show interface status
show_interface_status() {
    echo "Current Interface Status:"
    echo "========================"
    
    for iface in eth2 eth3 eth4; do
        if ip link show "$iface" >/dev/null 2>&1; then
            local status=$(ip link show "$iface" | grep -o "state [A-Z]*" | cut -d' ' -f2)
            local speed=$(ethtool "$iface" 2>/dev/null | grep "Speed:" | awk '{print $2}' || echo "Unknown")
            print_colored "green" "  $iface: $status, Speed: $speed"
        else
            print_colored "red" "  $iface: Not found"
        fi
    done
    echo
}

# Function to show I2C device status
show_i2c_status() {
    echo "I2C Device Status:"
    echo "=================="
    
    if command -v i2cdetect >/dev/null 2>&1; then
        local i2c_result=$(i2cdetect -y 1 2>/dev/null | grep -E "77|UU" || echo "")
        if [[ -n "$i2c_result" ]]; then
            print_colored "green" "  I2C device found at address 0x77 on bus 1"
        else
            print_colored "red" "  No I2C device found at address 0x77 on bus 1"
        fi
    else
        print_colored "yellow" "  i2c-tools not available"
    fi
    echo
}

# Function to show debug message categories
show_debug_categories() {
    echo "Debug Message Categories:"
    echo "========================"
    print_colored "blue"   "  MAC Messages:     [DPAA2-ETH] (Blue/Cyan)"
    print_colored "purple" "  PHY Messages:     [88x5113-PHY] (Purple)"
    print_colored "yellow" "  I2C Operations:   I2C READ/WRITE (Yellow)"
    print_colored "green"  "  Success:          ✓ messages (Green)"
    print_colored "red"    "  Errors:           ✗ messages (Red)"
    echo
}

# Function to generate test traffic
generate_test_traffic() {
    echo "Generating test traffic on 88x5113 interfaces..."
    
    for iface in eth2 eth3 eth4; do
        if ip link show "$iface" >/dev/null 2>&1; then
            echo "  Bringing up $iface..."
            ip link set "$iface" up 2>/dev/null || true
            sleep 1
        fi
    done
    
    echo "  Test traffic generation complete"
    echo
}

# Main menu
show_menu() {
    echo "Debug Options:"
    echo "=============="
    echo "1. Monitor real-time debug messages"
    echo "2. Show recent debug messages"
    echo "3. Show interface status"
    echo "4. Show I2C device status"
    echo "5. Generate test traffic"
    echo "6. Show all status information"
    echo "7. Exit"
    echo
}

# Main execution
case "${1:-menu}" in
    "monitor"|"1")
        show_debug_categories
        monitor_kernel_messages
        ;;
    "recent"|"2")
        show_recent_messages
        ;;
    "interfaces"|"3")
        show_interface_status
        ;;
    "i2c"|"4")
        show_i2c_status
        ;;
    "traffic"|"5")
        generate_test_traffic
        ;;
    "status"|"6")
        show_recent_messages
        show_interface_status
        show_i2c_status
        ;;
    "menu"|*)
        while true; do
            show_menu
            read -p "Select option (1-7): " choice
            case $choice in
                1) show_debug_categories; monitor_kernel_messages; break ;;
                2) show_recent_messages ;;
                3) show_interface_status ;;
                4) show_i2c_status ;;
                5) generate_test_traffic ;;
                6) show_recent_messages; show_interface_status; show_i2c_status ;;
                7) echo "Exiting..."; exit 0 ;;
                *) echo "Invalid option. Please select 1-7." ;;
            esac
            echo
        done
        ;;
esac

echo "Debug trace monitoring complete."
echo "Log file saved to: $LOG_FILE"
